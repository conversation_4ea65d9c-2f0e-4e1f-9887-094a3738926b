package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"topnetwork.ai/topai/chat-webserver/analysis/repository"
	"topnetwork.ai/topai/chat-webserver/analysis/repository/models"
	"topnetwork.ai/topai/chat-webserver/analysis/service"
)

// 这个文件展示了如何使用支付审计系统的各个组件

func main() {
	// 初始化数据库连接
	dsn := "root:password@tcp(localhost:3306)/payment_analysis?charset=utf8mb4&parseTime=True&loc=Local"
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// 自动迁移表结构
	err = db.AutoMigrate(
		&models.StripeEvent{},
		&models.PaymentEvent{},
		&models.UserPaymentDailyStats{},
		&models.PaymentFailureStats{},
		&models.UserCardUsage{},
		&models.GeoRiskConfig{},
	)
	if err != nil {
		log.Fatalf("Failed to migrate database: %v", err)
	}

	// 示例1：插入测试数据
	insertTestData(db)

	// 示例2：使用DAO层查询数据
	testDAO(db)

	// 示例3：使用分析服务
	testAnalysisService(db)

	// 示例4：使用定时任务服务
	testSchedulerService(db)

	log.Println("All tests completed successfully!")
}

// insertTestData 插入测试数据
func insertTestData(db *gorm.DB) {
	log.Println("Inserting test data...")

	// 创建测试Stripe事件
	testEvents := []models.StripeEvent{
		{
			EventID:         "evt_test_001",
			EventType:       "payment_intent.succeeded",
			Payload:         createTestPayload("succeeded", "cus_test123", 2000, "usd"),
			CreatedAtStripe: time.Now().Add(-2 * time.Hour),
			Processed:       false,
		},
		{
			EventID:         "evt_test_002",
			EventType:       "payment_intent.payment_failed",
			Payload:         createTestPayload("failed", "cus_test123", 1500, "usd"),
			CreatedAtStripe: time.Now().Add(-1 * time.Hour),
			Processed:       false,
		},
		{
			EventID:         "evt_test_003",
			EventType:       "charge.dispute.created",
			Payload:         createTestDispute("cus_test123", 2000, "fraudulent"),
			CreatedAtStripe: time.Now().Add(-30 * time.Minute),
			Processed:       false,
		},
	}

	for _, event := range testEvents {
		db.Create(&event)
	}

	log.Printf("Inserted %d test events", len(testEvents))
}

// createTestPayload 创建测试支付负载
func createTestPayload(status, customerID string, amount int, currency string) string {
	payload := map[string]interface{}{
		"object": map[string]interface{}{
			"id":       "pi_test_" + status,
			"customer": customerID,
			"amount":   amount,
			"currency": currency,
			"status":   status,
			"charges": map[string]interface{}{
				"data": []map[string]interface{}{
					{
						"payment_method_details": map[string]interface{}{
							"card": map[string]interface{}{
								"brand":   "visa",
								"last4":   "4242",
								"country": "US",
							},
						},
					},
				},
			},
		},
	}

	if status == "failed" {
		payload["object"].(map[string]interface{})["last_payment_error"] = map[string]interface{}{
			"code":    "card_declined",
			"message": "Your card was declined.",
		}
	}

	jsonData, _ := json.Marshal(payload)
	return string(jsonData)
}

// createTestDispute 创建测试争议负载
func createTestDispute(customerID string, amount int, reason string) string {
	payload := map[string]interface{}{
		"object": map[string]interface{}{
			"id":       "dp_test_dispute",
			"amount":   amount,
			"currency": "usd",
			"reason":   reason,
			"charge": map[string]interface{}{
				"customer": customerID,
			},
		},
	}

	jsonData, _ := json.Marshal(payload)
	return string(jsonData)
}

// testDAO 测试DAO层功能
func testDAO(db *gorm.DB) {
	log.Println("Testing DAO layer...")

	stripeDAO := repository.NewStripeEventDAO(db)

	// 测试按时间范围查询
	startTime := time.Now().Add(-3 * time.Hour)
	endTime := time.Now()

	events, err := stripeDAO.GetStripeEventsByTimeRange(startTime, endTime, nil)
	if err != nil {
		log.Printf("Failed to get events by time range: %v", err)
		return
	}

	log.Printf("Found %d events in time range", len(events))

	// 测试获取未处理事件
	unprocessedEvents, err := stripeDAO.GetUnprocessedEvents(10)
	if err != nil {
		log.Printf("Failed to get unprocessed events: %v", err)
		return
	}

	log.Printf("Found %d unprocessed events", len(unprocessedEvents))

	// 测试事件类型统计
	eventCounts, err := stripeDAO.GetEventCountByType(startTime, endTime)
	if err != nil {
		log.Printf("Failed to get event counts: %v", err)
		return
	}

	log.Println("Event counts by type:")
	for eventType, count := range eventCounts {
		log.Printf("  %s: %d", eventType, count)
	}
}

// testAnalysisService 测试分析服务
func testAnalysisService(db *gorm.DB) {
	log.Println("Testing analysis service...")

	analysisService := service.NewPaymentAnalysisService(db)

	// 测试用户分析
	userID := "cus_test123"
	timeRange := service.TimeRange{
		StartTime: time.Now().Add(-3 * time.Hour),
		EndTime:   time.Now(),
	}

	userAnalysis, err := analysisService.AnalyzeUserPaymentsByTimeRange(context.Background(), userID, timeRange)
	if err != nil {
		log.Printf("Failed to analyze user payments: %v", err)
		return
	}

	log.Printf("User Analysis Results for %s:", userID)
	log.Printf("  Total Payments: %d", userAnalysis.TotalPayments)
	log.Printf("  Successful Payments: %d", userAnalysis.SuccessfulPayments)
	log.Printf("  Failed Payments: %d", userAnalysis.FailedPayments)
	log.Printf("  Disputed Payments: %d", userAnalysis.DisputedPayments)
	log.Printf("  Payment Failure Rate: %.2f%%", userAnalysis.PaymentFailureRate*100)
	log.Printf("  Dispute Rate: %.2f%%", userAnalysis.DisputeRate*100)
	log.Printf("  Unique Cards Used: %d", userAnalysis.UniqueCardsUsed)
	log.Printf("  Charge Frequency: %.2f per day", userAnalysis.ChargeFrequency)

	if len(userAnalysis.FailureReasons) > 0 {
		log.Println("  Failure Reasons:")
		for reason, count := range userAnalysis.FailureReasons {
			log.Printf("    %s: %d", reason, count)
		}
	}

	if len(userAnalysis.CardDetails) > 0 {
		log.Println("  Card Details:")
		for _, card := range userAnalysis.CardDetails {
			log.Printf("    %s *%s (%s): %d uses, %d success, %d failed",
				card.CardBrand, card.CardLast4, card.CardCountry,
				card.UsageCount, card.SuccessCount, card.FailCount)
		}
	}
}

// testSchedulerService 测试定时任务服务
func testSchedulerService(db *gorm.DB) {
	log.Println("Testing scheduler service...")

	schedulerService := service.NewSchedulerService(db)

	// 创建测试配置
	config := service.AnalysisConfig{
		BatchSize:            10,
		AnalysisInterval:     1 * time.Minute, // 1分钟间隔用于测试
		TimeWindowHours:      3,               // 分析过去3小时
		EnableUserAnalysis:   true,
		EnableSystemAnalysis: true,
	}

	// 启动调度器
	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Minute)
	defer cancel()

	err := schedulerService.Start(ctx, config)
	if err != nil {
		log.Printf("Failed to start scheduler: %v", err)
		return
	}

	log.Println("Scheduler started, waiting for analysis to complete...")

	// 等待一段时间让调度器运行
	time.Sleep(30 * time.Second)

	// 停止调度器
	schedulerService.Stop()
	log.Println("Scheduler stopped")
}

// testAnalysisManager 测试分析管理器
func testAnalysisManager(db *gorm.DB) {
	log.Println("Testing analysis manager...")

	analysisManager := service.NewAnalysisManager(db)

	// 测试用户分析
	userID := "cus_test123"
	hours := 3

	userReport, err := analysisManager.GetUserAnalysis(context.Background(), userID, hours)
	if err != nil {
		log.Printf("Failed to get user analysis: %v", err)
		return
	}

	log.Printf("User Analysis Report for %s:", userID)
	log.Printf("  Generated at: %s", userReport.GeneratedAt.Format(time.RFC3339))
	
	if userReport.PaymentAnalysis != nil {
		pa := userReport.PaymentAnalysis
		log.Printf("  Payment Analysis:")
		log.Printf("    Total Payments: %d", pa.TotalPayments)
		log.Printf("    Failure Rate: %.2f%%", pa.PaymentFailureRate*100)
		log.Printf("    Dispute Rate: %.2f%%", pa.DisputeRate*100)
	}

	if userReport.FailureAnalysis != nil {
		fa := userReport.FailureAnalysis
		log.Printf("  Failure Analysis:")
		log.Printf("    Total Failures: %d", fa.TotalFailures)
		log.Printf("    Recommendations: %v", fa.Recommendations)
	}

	if userReport.GeoRiskAssessment != nil {
		gra := userReport.GeoRiskAssessment
		log.Printf("  Geo Risk Assessment:")
		log.Printf("    Risk Level: %s", gra.RiskSummary.RiskLevel)
		log.Printf("    Total Countries: %d", gra.RiskSummary.TotalCountries)
	}

	// 测试系统分析
	systemReport, err := analysisManager.GetSystemAnalysis(context.Background(), hours)
	if err != nil {
		log.Printf("Failed to get system analysis: %v", err)
		return
	}

	log.Printf("System Analysis Report:")
	log.Printf("  Active Users: %d", systemReport.ActiveUsers)
	log.Printf("  Total Events: %d", systemReport.SystemMetrics.TotalEvents)
	log.Printf("  Success Rate: %.2f%%", systemReport.SystemMetrics.SuccessRate*100)
	log.Printf("  High Risk Users: %d", len(systemReport.HighRiskUsers))
}
