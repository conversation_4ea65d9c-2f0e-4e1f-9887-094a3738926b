package service

import (
	"context"
	"fmt"
	"time"

	"topnetwork.ai/topai/chat-webserver/analysis/repository"
	"topnetwork.ai/topai/chat-webserver/analysis/repository/models"
)

// GeoRiskService 地理风险评估服务
type GeoRiskService struct {
	db *repository.DB
}

// NewGeoRiskService 创建地理风险评估服务实例
func NewGeoRiskService(db *repository.DB) *GeoRiskService {
	return &GeoRiskService{
		db: db,
	}
}

// GeoRiskAssessment 地理风险评估结果
type GeoRiskAssessment struct {
	UserID           string                `json:"user_id"`
	TimeRange        TimeRange             `json:"time_range"`
	CountryAnalysis  []CountryRiskAnalysis `json:"country_analysis"`
	RiskSummary      GeoRiskSummary        `json:"risk_summary"`
	Recommendations  []string              `json:"recommendations"`
	BlockedCountries []string              `json:"blocked_countries"`
}

// CountryRiskAnalysis 国家风险分析
type CountryRiskAnalysis struct {
	CountryCode     string    `json:"country_code"`
	CountryName     string    `json:"country_name"`
	RiskLevel       string    `json:"risk_level"`
	IsAllowed       bool      `json:"is_allowed"`
	PaymentCount    int       `json:"payment_count"`
	SuccessfulCount int       `json:"successful_count"`
	FailedCount     int       `json:"failed_count"`
	DisputeCount    int       `json:"dispute_count"`
	SuccessRate     float64   `json:"success_rate"`
	FailureRate     float64   `json:"failure_rate"`
	DisputeRate     float64   `json:"dispute_rate"`
	TotalAmount     float64   `json:"total_amount"`
	FirstUsed       time.Time `json:"first_used"`
	LastUsed        time.Time `json:"last_used"`
	UniqueCards     int       `json:"unique_cards"`
}

// GeoRiskSummary 地理风险摘要
type GeoRiskSummary struct {
	TotalCountries      int     `json:"total_countries"`
	LowRiskCountries    int     `json:"low_risk_countries"`
	MediumRiskCountries int     `json:"medium_risk_countries"`
	HighRiskCountries   int     `json:"high_risk_countries"`
	BlockedCountries    int     `json:"blocked_countries"`
	OverallRiskScore    float64 `json:"overall_risk_score"`
	RiskLevel           string  `json:"risk_level"`
}

// CountryInfo 国家信息映射
var countryNames = map[string]string{
	"US": "United States",
	"CA": "Canada",
	"GB": "United Kingdom",
	"AU": "Australia",
	"DE": "Germany",
	"FR": "France",
	"JP": "Japan",
	"CN": "China",
	"RU": "Russia",
	"IR": "Iran",
	"KP": "North Korea",
	"IN": "India",
	"BR": "Brazil",
	"MX": "Mexico",
	"IT": "Italy",
	"ES": "Spain",
	"NL": "Netherlands",
	"SE": "Sweden",
	"NO": "Norway",
	"DK": "Denmark",
}

// AssessGeoRisk 评估用户的地理风险
func (s *GeoRiskService) AssessGeoRisk(ctx context.Context, userID string, timeRange TimeRange) (*GeoRiskAssessment, error) {
	assessment := &GeoRiskAssessment{
		UserID:           userID,
		TimeRange:        timeRange,
		CountryAnalysis:  []CountryRiskAnalysis{},
		Recommendations:  []string{},
		BlockedCountries: []string{},
	}

	// 获取用户在指定时间范围内的支付事件，按国家分组
	var countryStats []struct {
		Country         string
		PaymentCount    int64
		SuccessfulCount int64
		FailedCount     int64
		DisputeCount    int64
		TotalAmount     float64
		FirstUsed       time.Time
		LastUsed        time.Time
		UniqueCards     int64
	}

	err := s.db.GetDB().Model(&models.PaymentEvent{}).
		Select(`card_country as country,
			COUNT(*) as payment_count,
			SUM(CASE WHEN event_type = 'payment_succeeded' THEN 1 ELSE 0 END) as successful_count,
			SUM(CASE WHEN event_type = 'payment_failed' THEN 1 ELSE 0 END) as failed_count,
			SUM(CASE WHEN event_type = 'dispute_created' THEN 1 ELSE 0 END) as dispute_count,
			COALESCE(SUM(amount), 0) as total_amount,
			MIN(created_at) as first_used,
			MAX(created_at) as last_used,
			COUNT(DISTINCT CONCAT(card_brand, '_', card_last4)) as unique_cards`).
		Where("user_id = ? AND created_at BETWEEN ? AND ? AND card_country IS NOT NULL",
			userID, timeRange.StartTime, timeRange.EndTime).
		Group("card_country").
		Scan(&countryStats).Error
	if err != nil {
		return nil, fmt.Errorf("failed to fetch country stats: %w", err)
	}

	// 分析每个国家的风险
	for _, stat := range countryStats {
		analysis := s.analyzeCountryRisk(stat)
		assessment.CountryAnalysis = append(assessment.CountryAnalysis, analysis)

		// 收集被阻止的国家
		if !analysis.IsAllowed {
			assessment.BlockedCountries = append(assessment.BlockedCountries, analysis.CountryCode)
		}
	}

	// 计算风险摘要
	assessment.RiskSummary = s.calculateRiskSummary(assessment.CountryAnalysis)

	// 生成建议
	assessment.Recommendations = s.generateGeoRecommendations(assessment)

	return assessment, nil
}

// analyzeCountryRisk 分析单个国家的风险
func (s *GeoRiskService) analyzeCountryRisk(stat struct {
	Country         string
	PaymentCount    int64
	SuccessfulCount int64
	FailedCount     int64
	DisputeCount    int64
	TotalAmount     float64
	FirstUsed       time.Time
	LastUsed        time.Time
	UniqueCards     int64
}) CountryRiskAnalysis {

	analysis := CountryRiskAnalysis{
		CountryCode:     stat.Country,
		CountryName:     s.getCountryName(stat.Country),
		PaymentCount:    int(stat.PaymentCount),
		SuccessfulCount: int(stat.SuccessfulCount),
		FailedCount:     int(stat.FailedCount),
		DisputeCount:    int(stat.DisputeCount),
		TotalAmount:     stat.TotalAmount,
		FirstUsed:       stat.FirstUsed,
		LastUsed:        stat.LastUsed,
		UniqueCards:     int(stat.UniqueCards),
	}

	// 计算比率
	if analysis.PaymentCount > 0 {
		analysis.SuccessRate = float64(analysis.SuccessfulCount) / float64(analysis.PaymentCount)
		analysis.FailureRate = float64(analysis.FailedCount) / float64(analysis.PaymentCount)
		analysis.DisputeRate = float64(analysis.DisputeCount) / float64(analysis.PaymentCount)
	}

	// 获取风险配置
	var riskConfig models.GeoRiskConfig
	err := s.db.GetDB().Where("country_code = ?", stat.Country).First(&riskConfig).Error
	if err == nil {
		analysis.RiskLevel = string(riskConfig.RiskLevel)
		analysis.IsAllowed = riskConfig.IsAllowed
	} else {
		// 默认配置
		analysis.RiskLevel = "unknown"
		analysis.IsAllowed = true
	}

	return analysis
}

// calculateRiskSummary 计算风险摘要
func (s *GeoRiskService) calculateRiskSummary(countryAnalysis []CountryRiskAnalysis) GeoRiskSummary {
	summary := GeoRiskSummary{
		TotalCountries: len(countryAnalysis),
	}

	var totalRiskScore float64
	var totalPayments int

	for _, analysis := range countryAnalysis {
		totalPayments += analysis.PaymentCount

		switch analysis.RiskLevel {
		case "low":
			summary.LowRiskCountries++
			totalRiskScore += 1.0 * float64(analysis.PaymentCount)
		case "medium":
			summary.MediumRiskCountries++
			totalRiskScore += 2.0 * float64(analysis.PaymentCount)
		case "high":
			summary.HighRiskCountries++
			totalRiskScore += 3.0 * float64(analysis.PaymentCount)
		case "blocked":
			summary.BlockedCountries++
			totalRiskScore += 4.0 * float64(analysis.PaymentCount)
		default:
			totalRiskScore += 1.5 * float64(analysis.PaymentCount) // unknown默认为中等风险
		}
	}

	// 计算加权平均风险评分
	if totalPayments > 0 {
		summary.OverallRiskScore = totalRiskScore / float64(totalPayments)
	}

	// 确定整体风险等级
	if summary.OverallRiskScore <= 1.5 {
		summary.RiskLevel = "low"
	} else if summary.OverallRiskScore <= 2.5 {
		summary.RiskLevel = "medium"
	} else if summary.OverallRiskScore <= 3.5 {
		summary.RiskLevel = "high"
	} else {
		summary.RiskLevel = "critical"
	}

	return summary
}

// generateGeoRecommendations 生成地理风险建议
func (s *GeoRiskService) generateGeoRecommendations(assessment *GeoRiskAssessment) []string {
	var recommendations []string

	// 基于整体风险等级的建议
	switch assessment.RiskSummary.RiskLevel {
	case "high", "critical":
		recommendations = append(recommendations, "整体地理风险较高，建议加强身份验证和交易监控")
	case "medium":
		recommendations = append(recommendations, "地理风险中等，建议定期审查交易模式")
	}

	// 基于被阻止国家的建议
	if len(assessment.BlockedCountries) > 0 {
		recommendations = append(recommendations, fmt.Sprintf("检测到来自被阻止国家的交易：%v，建议立即审查", assessment.BlockedCountries))
	}

	// 基于高风险国家的建议
	highRiskCount := 0
	for _, analysis := range assessment.CountryAnalysis {
		if analysis.RiskLevel == "high" && analysis.PaymentCount > 0 {
			highRiskCount++
		}
	}
	if highRiskCount > 0 {
		recommendations = append(recommendations, "存在来自高风险国家的交易，建议增强验证措施")
	}

	// 基于失败率的建议
	for _, analysis := range assessment.CountryAnalysis {
		if analysis.FailureRate > 0.5 && analysis.PaymentCount >= 3 {
			recommendations = append(recommendations, fmt.Sprintf("来自%s的交易失败率较高(%.1f%%)，建议检查支付方式兼容性", analysis.CountryName, analysis.FailureRate*100))
		}
	}

	// 基于争议率的建议
	for _, analysis := range assessment.CountryAnalysis {
		if analysis.DisputeRate > 0.2 && analysis.PaymentCount >= 5 {
			recommendations = append(recommendations, fmt.Sprintf("来自%s的交易争议率较高(%.1f%%)，建议改善客户服务", analysis.CountryName, analysis.DisputeRate*100))
		}
	}

	return recommendations
}

// getCountryName 获取国家名称
func (s *GeoRiskService) getCountryName(countryCode string) string {
	if name, exists := countryNames[countryCode]; exists {
		return name
	}
	return countryCode
}

// UpdateGeoRiskConfig 更新地理风险配置
func (s *GeoRiskService) UpdateGeoRiskConfig(ctx context.Context, countryCode string, riskLevel models.RiskLevel, isAllowed bool, description string) error {
	config := models.GeoRiskConfig{
		CountryCode: countryCode,
		RiskLevel:   riskLevel,
		IsAllowed:   isAllowed,
		Description: &description,
	}

	return s.db.GetDB().Save(&config).Error
}

// GetGeoRiskConfig 获取地理风险配置
func (s *GeoRiskService) GetGeoRiskConfig(ctx context.Context) ([]models.GeoRiskConfig, error) {
	var configs []models.GeoRiskConfig
	err := s.db.GetDB().Find(&configs).Error
	return configs, err
}

// IsCountryAllowed 检查国家是否被允许
func (s *GeoRiskService) IsCountryAllowed(ctx context.Context, countryCode string) (bool, models.RiskLevel, error) {
	var config models.GeoRiskConfig
	err := s.db.GetDB().Where("country_code = ?", countryCode).First(&config).Error
	if err != nil {
		// 如果没有配置，默认允许且为低风险
		return true, models.RiskLevelLow, nil
	}
	return config.IsAllowed, config.RiskLevel, nil
}
