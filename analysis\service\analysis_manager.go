package service

import (
	"context"
	"fmt"
	"log"
	"time"

	"gorm.io/gorm"
	"topnetwork.ai/topai/chat-webserver/analysis/repository"
)

// AnalysisManager 分析管理器
type AnalysisManager struct {
	db                     *gorm.DB
	schedulerService       *SchedulerService
	paymentAnalysisService *PaymentAnalysisService
	failureAnalysisService *FailureAnalysisService
	geoRiskService         *GeoRiskService
	stripeEventDAO         *repository.StripeEventDAO
}

// NewAnalysisManager 创建分析管理器实例
func NewAnalysisManager(db *gorm.DB) *AnalysisManager {
	return &AnalysisManager{
		db:                     db,
		schedulerService:       NewSchedulerService(db),
		paymentAnalysisService: NewPaymentAnalysisService(db),
		failureAnalysisService: NewFailureAnalysisService(db),
		geoRiskService:         NewGeoRiskService(db),
		stripeEventDAO:         repository.NewStripeEventDAO(db),
	}
}

// StartAnalysis 启动分析服务
func (am *AnalysisManager) StartAnalysis(ctx context.Context) error {
	log.Println("Starting Payment Analysis Manager...")

	// 使用默认配置启动调度器
	config := DefaultAnalysisConfig()
	
	// 可以根据需要调整配置
	config.AnalysisInterval = 15 * time.Minute // 每15分钟分析一次
	config.TimeWindowHours = 6                 // 分析过去6小时的数据
	config.BatchSize = 50                      // 每批处理50个事件

	return am.schedulerService.Start(ctx, config)
}

// StopAnalysis 停止分析服务
func (am *AnalysisManager) StopAnalysis() {
	log.Println("Stopping Payment Analysis Manager...")
	am.schedulerService.Stop()
}

// GetUserAnalysis 获取用户分析报告
func (am *AnalysisManager) GetUserAnalysis(ctx context.Context, userID string, hours int) (*UserAnalysisReport, error) {
	endTime := time.Now()
	startTime := endTime.Add(-time.Duration(hours) * time.Hour)
	timeRange := TimeRange{
		StartTime: startTime,
		EndTime:   endTime,
	}

	// 获取基础支付分析
	paymentAnalysis, err := am.paymentAnalysisService.AnalyzeUserPaymentsByTimeRange(ctx, userID, timeRange)
	if err != nil {
		return nil, fmt.Errorf("failed to get payment analysis: %w", err)
	}

	// 获取失败原因分析
	failureAnalysis, err := am.failureAnalysisService.AnalyzeFailures(ctx, userID, timeRange)
	if err != nil {
		log.Printf("Failed to get failure analysis for user %s: %v", userID, err)
		// 不返回错误，继续处理其他分析
	}

	// 获取地理风险评估
	geoRiskAssessment, err := am.geoRiskService.AssessGeoRisk(ctx, userID, timeRange)
	if err != nil {
		log.Printf("Failed to get geo risk assessment for user %s: %v", userID, err)
		// 不返回错误，继续处理其他分析
	}

	// 组合分析报告
	report := &UserAnalysisReport{
		UserID:            userID,
		TimeRange:         timeRange,
		PaymentAnalysis:   paymentAnalysis,
		FailureAnalysis:   failureAnalysis,
		GeoRiskAssessment: geoRiskAssessment,
		GeneratedAt:       time.Now(),
	}

	return report, nil
}

// UserAnalysisReport 用户分析报告
type UserAnalysisReport struct {
	UserID            string                 `json:"user_id"`
	TimeRange         TimeRange              `json:"time_range"`
	PaymentAnalysis   *UserPaymentAnalysis   `json:"payment_analysis"`
	FailureAnalysis   *FailureAnalysisResult `json:"failure_analysis,omitempty"`
	GeoRiskAssessment *GeoRiskAssessment     `json:"geo_risk_assessment,omitempty"`
	GeneratedAt       time.Time              `json:"generated_at"`
}

// GetSystemAnalysis 获取系统级分析报告
func (am *AnalysisManager) GetSystemAnalysis(ctx context.Context, hours int) (*SystemAnalysisReport, error) {
	endTime := time.Now()
	startTime := endTime.Add(-time.Duration(hours) * time.Hour)
	timeRange := TimeRange{
		StartTime: startTime,
		EndTime:   endTime,
	}

	// 获取事件统计
	eventCounts, err := am.stripeEventDAO.GetEventCountByType(startTime, endTime)
	if err != nil {
		return nil, fmt.Errorf("failed to get event counts: %w", err)
	}

	// 计算系统指标
	systemMetrics := am.calculateSystemMetrics(eventCounts)

	// 获取活跃用户数
	activeUsers, err := am.getActiveUsersCount(timeRange)
	if err != nil {
		log.Printf("Failed to get active users count: %v", err)
		activeUsers = 0
	}

	// 获取高风险用户
	highRiskUsers, err := am.getHighRiskUsers(ctx, timeRange, 10)
	if err != nil {
		log.Printf("Failed to get high risk users: %v", err)
	}

	report := &SystemAnalysisReport{
		TimeRange:     timeRange,
		SystemMetrics: systemMetrics,
		ActiveUsers:   activeUsers,
		HighRiskUsers: highRiskUsers,
		GeneratedAt:   time.Now(),
	}

	return report, nil
}

// SystemAnalysisReport 系统分析报告
type SystemAnalysisReport struct {
	TimeRange     TimeRange        `json:"time_range"`
	SystemMetrics SystemMetrics    `json:"system_metrics"`
	ActiveUsers   int              `json:"active_users"`
	HighRiskUsers []UserRiskSummary `json:"high_risk_users,omitempty"`
	GeneratedAt   time.Time        `json:"generated_at"`
}

// UserRiskSummary 用户风险摘要
type UserRiskSummary struct {
	UserID        string  `json:"user_id"`
	RiskScore     float64 `json:"risk_score"`
	FailureRate   float64 `json:"failure_rate"`
	DisputeRate   float64 `json:"dispute_rate"`
	TotalPayments int     `json:"total_payments"`
	RiskFlags     []string `json:"risk_flags"`
}

// calculateSystemMetrics 计算系统指标
func (am *AnalysisManager) calculateSystemMetrics(eventCounts map[string]int64) SystemMetrics {
	var totalEvents int64
	var successEvents int64
	var failureEvents int64
	var disputeEvents int64

	for eventType, count := range eventCounts {
		totalEvents += count
		
		switch {
		case containsAny(eventType, []string{"succeeded", "success"}):
			successEvents += count
		case containsAny(eventType, []string{"failed", "failure"}):
			failureEvents += count
		case containsAny(eventType, []string{"dispute"}):
			disputeEvents += count
		}
	}

	metrics := SystemMetrics{
		TotalEvents: totalEvents,
		EventCounts: eventCounts,
	}

	if totalEvents > 0 {
		metrics.SuccessRate = float64(successEvents) / float64(totalEvents)
		metrics.FailureRate = float64(failureEvents) / float64(totalEvents)
		metrics.DisputeRate = float64(disputeEvents) / float64(totalEvents)
	}

	return metrics
}

// getActiveUsersCount 获取活跃用户数量
func (am *AnalysisManager) getActiveUsersCount(timeRange TimeRange) (int, error) {
	var count int64
	
	err := am.db.Raw(`
		SELECT COUNT(DISTINCT JSON_UNQUOTE(JSON_EXTRACT(payload, '$.object.customer'))) 
		FROM stripe_events 
		WHERE created_at_stripe >= ? AND created_at_stripe <= ?
			AND payload LIKE '%"customer"%'
			AND JSON_UNQUOTE(JSON_EXTRACT(payload, '$.object.customer')) IS NOT NULL
			AND JSON_UNQUOTE(JSON_EXTRACT(payload, '$.object.customer')) != ''
			AND JSON_UNQUOTE(JSON_EXTRACT(payload, '$.object.customer')) != 'null'
	`, timeRange.StartTime, timeRange.EndTime).Scan(&count).Error
	
	return int(count), err
}

// getHighRiskUsers 获取高风险用户
func (am *AnalysisManager) getHighRiskUsers(ctx context.Context, timeRange TimeRange, limit int) ([]UserRiskSummary, error) {
	// 获取活跃用户列表
	userIDs, err := am.getActiveUsersList(timeRange)
	if err != nil {
		return nil, err
	}

	var highRiskUsers []UserRiskSummary

	// 分析每个用户的风险
	for _, userID := range userIDs {
		analysis, err := am.paymentAnalysisService.AnalyzeUserPaymentsByTimeRange(ctx, userID, timeRange)
		if err != nil {
			continue
		}

		// 计算风险评分
		riskScore := am.calculateUserRiskScore(analysis)
		
		// 只包含高风险用户（风险评分 > 0.3）
		if riskScore > 0.3 {
			riskFlags := am.generateRiskFlags(analysis)
			
			highRiskUsers = append(highRiskUsers, UserRiskSummary{
				UserID:        userID,
				RiskScore:     riskScore,
				FailureRate:   analysis.PaymentFailureRate,
				DisputeRate:   analysis.DisputeRate,
				TotalPayments: analysis.TotalPayments,
				RiskFlags:     riskFlags,
			})
		}

		// 限制返回数量
		if len(highRiskUsers) >= limit {
			break
		}
	}

	return highRiskUsers, nil
}

// getActiveUsersList 获取活跃用户列表
func (am *AnalysisManager) getActiveUsersList(timeRange TimeRange) ([]string, error) {
	var userIDs []string
	
	rows, err := am.db.Raw(`
		SELECT DISTINCT 
			JSON_UNQUOTE(JSON_EXTRACT(payload, '$.object.customer')) as user_id
		FROM stripe_events 
		WHERE created_at_stripe >= ? AND created_at_stripe <= ?
			AND payload LIKE '%"customer"%'
			AND JSON_UNQUOTE(JSON_EXTRACT(payload, '$.object.customer')) IS NOT NULL
			AND JSON_UNQUOTE(JSON_EXTRACT(payload, '$.object.customer')) != ''
			AND JSON_UNQUOTE(JSON_EXTRACT(payload, '$.object.customer')) != 'null'
		LIMIT 100
	`, timeRange.StartTime, timeRange.EndTime).Rows()
	
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	for rows.Next() {
		var userID string
		if err := rows.Scan(&userID); err != nil {
			continue
		}
		if userID != "" {
			userIDs = append(userIDs, userID)
		}
	}

	return userIDs, nil
}

// calculateUserRiskScore 计算用户风险评分
func (am *AnalysisManager) calculateUserRiskScore(analysis *UserPaymentAnalysis) float64 {
	if analysis.TotalPayments == 0 {
		return 0
	}

	// 风险评分计算：失败率 * 0.4 + 争议率 * 0.6
	riskScore := analysis.PaymentFailureRate*0.4 + analysis.DisputeRate*0.6

	// 如果使用多张卡片，增加风险评分
	if analysis.UniqueCardsUsed > 3 {
		riskScore += 0.1
	}

	// 如果充值频率过高，增加风险评分
	if analysis.ChargeFrequency > 5 {
		riskScore += 0.1
	}

	// 确保评分在0-1范围内
	if riskScore > 1 {
		riskScore = 1
	}

	return riskScore
}

// generateRiskFlags 生成风险标记
func (am *AnalysisManager) generateRiskFlags(analysis *UserPaymentAnalysis) []string {
	var flags []string

	if analysis.PaymentFailureRate > 0.3 {
		flags = append(flags, "high_failure_rate")
	}

	if analysis.DisputeRate > 0.1 {
		flags = append(flags, "high_dispute_rate")
	}

	if analysis.UniqueCardsUsed > 5 {
		flags = append(flags, "multiple_cards")
	}

	if analysis.ChargeFrequency > 10 {
		flags = append(flags, "frequent_charges")
	}

	return flags
}

// ManualAnalysis 手动触发分析
func (am *AnalysisManager) ManualAnalysis(ctx context.Context, userID string, hours int) (*UserAnalysisReport, error) {
	log.Printf("Manual analysis triggered for user %s, time window: %d hours", userID, hours)
	return am.GetUserAnalysis(ctx, userID, hours)
}

// containsAny 检查字符串是否包含任何一个子字符串
func containsAny(s string, substrs []string) bool {
	for _, substr := range substrs {
		if len(s) >= len(substr) {
			for i := 0; i <= len(s)-len(substr); i++ {
				if s[i:i+len(substr)] == substr {
					return true
				}
			}
		}
	}
	return false
}
