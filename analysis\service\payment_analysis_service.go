package service

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/stripe/stripe-go/v82"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"topnetwork.ai/topai/chat-webserver/analysis/repository"
	"topnetwork.ai/topai/chat-webserver/analysis/repository/models"
	"topnetwork.ai/topai/chat-webserver/logger"
)

// PaymentAnalysisService 支付分析服务
type PaymentAnalysisService struct {
	db             *gorm.DB
	stripeEventDAO *repository.StripeEventDAO
	log            *logger.ModuleLogger
}

// NewPaymentAnalysisService 创建支付分析服务实例
func NewPaymentAnalysisService(db *gorm.DB) *PaymentAnalysisService {
	return &PaymentAnalysisService{
		db:             db,
		stripeEventDAO: repository.NewStripeEventDAO(db),
		log:            logger.GetLogger("payment_analysis_service"),
	}
}

// UserPaymentAnalysis 用户支付分析结果
type UserPaymentAnalysis struct {
	UserID             string                `json:"user_id"`
	TimeRange          TimeRange             `json:"time_range"`
	PaymentFailureRate float64               `json:"payment_failure_rate"`
	DisputeRate        float64               `json:"dispute_rate"`
	ChargeFrequency    float64               `json:"charge_frequency"`
	UniqueCardsUsed    int                   `json:"unique_cards_used"`
	TotalPayments      int                   `json:"total_payments"`
	SuccessfulPayments int                   `json:"successful_payments"`
	FailedPayments     int                   `json:"failed_payments"`
	DisputedPayments   int                   `json:"disputed_payments"`
	FailureReasons     map[string]int        `json:"failure_reasons"`
	CardDetails        []CardUsageInfo       `json:"card_details"`
	PaymentTimeline    []PaymentTimelineItem `json:"payment_timeline"`
}

// CardUsageInfo 银行卡使用信息
type CardUsageInfo struct {
	CardBrand    string `json:"card_brand"`
	CardLast4    string `json:"card_last4"`
	CardCountry  string `json:"card_country"`
	UsageCount   int    `json:"usage_count"`
	SuccessCount int    `json:"success_count"`
	FailCount    int    `json:"fail_count"`
}

// PaymentTimelineItem 支付时间线项目
type PaymentTimelineItem struct {
	Timestamp   time.Time `json:"timestamp"`
	EventType   string    `json:"event_type"`
	Amount      float64   `json:"amount"`
	Currency    string    `json:"currency"`
	Status      string    `json:"status"`
	FailureCode string    `json:"failure_code,omitempty"`
}

// AnalyzeUserPaymentsByTimeRange 根据时间范围分析用户支付数据
func (s *PaymentAnalysisService) AnalyzeUserPaymentsByTimeRange(ctx context.Context, userID string, timeRange TimeRange) (*UserPaymentAnalysis, error) {
	// 获取用户在指定时间范围内的所有支付相关事件
	events, err := s.stripeEventDAO.GetEventsByUserAndTimeRange(userID, timeRange.StartTime, timeRange.EndTime)
	if err != nil {
		return nil, fmt.Errorf("failed to get user events: %w", err)
	}

	analysis := &UserPaymentAnalysis{
		UserID:          userID,
		TimeRange:       timeRange,
		FailureReasons:  make(map[string]int),
		CardDetails:     []CardUsageInfo{},
		PaymentTimeline: []PaymentTimelineItem{},
	}

	// 分析每个事件
	cardUsageMap := make(map[string]*CardUsageInfo)

	for _, event := range events {
		s.processEventForAnalysis(event, analysis, cardUsageMap)
	}

	// 转换卡片使用信息
	for _, cardInfo := range cardUsageMap {
		analysis.CardDetails = append(analysis.CardDetails, *cardInfo)
	}
	analysis.UniqueCardsUsed = len(cardUsageMap)

	// 计算比率
	s.calculateRates(analysis, timeRange)

	return analysis, nil
}

// processEventForAnalysis 处理单个事件进行分析
func (s *PaymentAnalysisService) processEventForAnalysis(event models.StripeEvent, analysis *UserPaymentAnalysis, cardUsageMap map[string]*CardUsageInfo) {
	if event.Payload == "" {
		s.log.Info("Empty payload", zap.String("event_id", event.EventID))
		return
	}

	// 根据事件类型处理
	switch event.EventType {
	case string(stripe.EventTypeChargeDisputeCreated):
	case string(stripe.EventTypeChargeDisputeFundsWithdrawn):
	case string(stripe.EventTypeChargeDisputeUpdated):
	case string(stripe.EventTypeChargeDisputeClosed):
	case string(stripe.EventTypeChargeDisputeFundsReinstated):
		s.processDispute([]byte(event.Payload), analysis, event.CreatedAtStripe)

	case string(stripe.EventTypePaymentIntentCreated):
	case string(stripe.EventTypePaymentIntentSucceeded):
	case string(stripe.EventTypePaymentIntentPaymentFailed):
	case string(stripe.EventTypePaymentIntentPartiallyFunded):
	case string(stripe.EventTypePaymentIntentCanceled):
		s.processSuccessfulPayment([]byte(event.Payload), analysis, cardUsageMap, event.CreatedAtStripe)

	case string(stripe.EventTypeChargeRefunded):
	case string(stripe.EventTypeRefundCreated):
	case string(stripe.EventTypeRefundFailed):
	case string(stripe.EventTypeRefundUpdated):
		s.processRefund([]byte(event.Payload), analysis, event.CreatedAtStripe)

	case string(stripe.EventTypeRadarEarlyFraudWarningCreated):
	case string(stripe.EventTypeRadarEarlyFraudWarningUpdated):
		s.processFraudWarning([]byte(event.Payload), analysis, event.CreatedAtStripe)
	default:
		s.log.Info("Unhandled:", zap.String("event_type", event.EventType))
	}
}

// processSuccessfulPayment 处理成功支付
func (s *PaymentAnalysisService) processSuccessfulPayment(payloadData map[string]interface{}, analysis *UserPaymentAnalysis, cardUsageMap map[string]*CardUsageInfo, timestamp time.Time) {
	analysis.TotalPayments++
	analysis.SuccessfulPayments++

	// 提取支付信息
	if obj, ok := payloadData["object"].(map[string]interface{}); ok {
		amount := s.extractFloat64(obj, "amount")
		currency := s.extractString(obj, "currency")

		// 添加到时间线
		analysis.PaymentTimeline = append(analysis.PaymentTimeline, PaymentTimelineItem{
			Timestamp: timestamp,
			EventType: "payment_succeeded",
			Amount:    amount / 100, // Stripe金额以分为单位
			Currency:  currency,
			Status:    "succeeded",
		})

		// 处理支付方法信息
		if charges, ok := obj["charges"].(map[string]interface{}); ok {
			if data, ok := charges["data"].([]interface{}); ok && len(data) > 0 {
				if charge, ok := data[0].(map[string]interface{}); ok {
					s.processCardInfo(charge, cardUsageMap, true)
				}
			}
		}
	}
}

// processFailedPayment 处理失败支付
func (s *PaymentAnalysisService) processFailedPayment(payloadData map[string]interface{}, analysis *UserPaymentAnalysis, cardUsageMap map[string]*CardUsageInfo, timestamp time.Time) {
	analysis.TotalPayments++
	analysis.FailedPayments++

	if obj, ok := payloadData["object"].(map[string]interface{}); ok {
		amount := s.extractFloat64(obj, "amount")
		currency := s.extractString(obj, "currency")

		// 提取失败原因
		failureCode := ""
		if lastPaymentError, ok := obj["last_payment_error"].(map[string]interface{}); ok {
			failureCode = s.extractString(lastPaymentError, "code")
			if failureCode != "" {
				analysis.FailureReasons[failureCode]++
			}
		}

		// 添加到时间线
		analysis.PaymentTimeline = append(analysis.PaymentTimeline, PaymentTimelineItem{
			Timestamp:   timestamp,
			EventType:   "payment_failed",
			Amount:      amount / 100,
			Currency:    currency,
			Status:      "failed",
			FailureCode: failureCode,
		})

		// 处理支付方法信息
		if charges, ok := obj["charges"].(map[string]interface{}); ok {
			if data, ok := charges["data"].([]interface{}); ok && len(data) > 0 {
				if charge, ok := data[0].(map[string]interface{}); ok {
					s.processCardInfo(charge, cardUsageMap, false)
				}
			}
		}
	}
}

// processDispute 处理争议
func (s *PaymentAnalysisService) processDispute(payloadData []byte, analysis *UserPaymentAnalysis, timestamp time.Time) {
	analysis.DisputedPayments++

	var dispute stripe.Dispute
	if err := json.Unmarshal(payloadData, &dispute); err != nil {
		s.log.Error("Failed to unmarshal dispute", zap.Error(err))
		return
	}

	if obj, ok := payloadData["object"].(map[string]interface{}); ok {
		amount := s.extractFloat64(obj, "amount")
		currency := s.extractString(obj, "currency")
		reason := s.extractString(obj, "reason")

		// 记录争议原因
		if reason != "" {
			analysis.FailureReasons["dispute_"+reason]++
		}

		// 添加到时间线
		analysis.PaymentTimeline = append(analysis.PaymentTimeline, PaymentTimelineItem{
			Timestamp:   timestamp,
			EventType:   "dispute_created",
			Amount:      amount / 100,
			Currency:    currency,
			Status:      "disputed",
			FailureCode: reason,
		})
	}
}

// processChargeSucceeded 处理charge成功事件
func (s *PaymentAnalysisService) processChargeSucceeded(payloadData map[string]interface{}, analysis *UserPaymentAnalysis, cardUsageMap map[string]*CardUsageInfo, timestamp time.Time) {
	if obj, ok := payloadData["object"].(map[string]interface{}); ok {
		s.processCardInfo(obj, cardUsageMap, true)
	}
}

// processChargeFailed 处理charge失败事件
func (s *PaymentAnalysisService) processChargeFailed(payloadData map[string]interface{}, analysis *UserPaymentAnalysis, cardUsageMap map[string]*CardUsageInfo, timestamp time.Time) {
	if obj, ok := payloadData["object"].(map[string]interface{}); ok {
		// 提取失败代码
		failureCode := s.extractString(obj, "failure_code")
		if failureCode != "" {
			analysis.FailureReasons[failureCode]++
		}

		// 提取outcome信息
		if outcome, ok := obj["outcome"].(map[string]interface{}); ok {
			outcomeType := s.extractString(outcome, "type")
			if outcomeType != "" {
				analysis.FailureReasons["outcome_"+outcomeType]++
			}
		}

		s.processCardInfo(obj, cardUsageMap, false)
	}
}

// processCardInfo 处理银行卡信息
func (s *PaymentAnalysisService) processCardInfo(chargeData map[string]interface{}, cardUsageMap map[string]*CardUsageInfo, isSuccess bool) {
	if paymentMethodDetails, ok := chargeData["payment_method_details"].(map[string]interface{}); ok {
		if card, ok := paymentMethodDetails["card"].(map[string]interface{}); ok {
			brand := s.extractString(card, "brand")
			last4 := s.extractString(card, "last4")
			country := s.extractString(card, "country")

			if brand != "" && last4 != "" {
				cardKey := fmt.Sprintf("%s_%s", brand, last4)

				if cardInfo, exists := cardUsageMap[cardKey]; exists {
					cardInfo.UsageCount++
					if isSuccess {
						cardInfo.SuccessCount++
					} else {
						cardInfo.FailCount++
					}
				} else {
					cardUsageMap[cardKey] = &CardUsageInfo{
						CardBrand:    brand,
						CardLast4:    last4,
						CardCountry:  country,
						UsageCount:   1,
						SuccessCount: 0,
						FailCount:    0,
					}
					if isSuccess {
						cardUsageMap[cardKey].SuccessCount = 1
					} else {
						cardUsageMap[cardKey].FailCount = 1
					}
				}
			}
		}
	}
}

// calculateRates 计算各种比率
func (s *PaymentAnalysisService) calculateRates(analysis *UserPaymentAnalysis, timeRange TimeRange) {
	if analysis.TotalPayments > 0 {
		analysis.PaymentFailureRate = float64(analysis.FailedPayments) / float64(analysis.TotalPayments)
		analysis.DisputeRate = float64(analysis.DisputedPayments) / float64(analysis.TotalPayments)
	}

	// 计算充值频率（次数/天）
	days := timeRange.EndTime.Sub(timeRange.StartTime).Hours() / 24
	if days > 0 {
		analysis.ChargeFrequency = float64(analysis.SuccessfulPayments) / days
	}
}

// extractString 从map中提取字符串值
func (s *PaymentAnalysisService) extractString(data map[string]interface{}, key string) string {
	if val, ok := data[key]; ok {
		if str, ok := val.(string); ok {
			return str
		}
	}
	return ""
}

// extractFloat64 从map中提取float64值
func (s *PaymentAnalysisService) extractFloat64(data map[string]interface{}, key string) float64 {
	if val, ok := data[key]; ok {
		switch v := val.(type) {
		case float64:
			return v
		case int:
			return float64(v)
		case int64:
			return float64(v)
		}
	}
	return 0
}
