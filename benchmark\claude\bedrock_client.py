#!/usr/bin/env python3
"""
AWS Bedrock AI Client Implementation
Implements the unified AI client interface for AWS Bedrock API
"""

import boto3
import json
import asyncio
from typing import List, Dict, Any, Optional, AsyncGenerator
import logging
from concurrent.futures import ThreadPoolExecutor

try:
    from .base_ai_client import (
        BaseAIClient, CompletionRequest, CompletionResponse,
        ChatMessage, ModelConfig, TestResult
    )
except ImportError:
    from base_ai_client import (
        BaseAIClient, CompletionRequest, CompletionResponse,
        ChatMessage, ModelConfig, TestResult
    )

logger = logging.getLogger(__name__)

class BedrockClient(BaseAIClient):
    """AWS Bedrock API client implementation"""

    def __init__(self, region_name: str = 'us-west-2', aws_access_key_id: Optional[str] = None,
                 aws_secret_access_key: Optional[str] = None, aws_session_token: Optional[str] = None):
        self.region_name = region_name

        # Initialize Bedrock runtime client
        session_kwargs = {'region_name': region_name}
        self.bedrock_runtime = boto3.client('bedrock-runtime', **session_kwargs)
        self.executor = ThreadPoolExecutor(max_workers=15)

        super().__init__("bedrock")

    def _initialize_models(self) -> None:
        """Initialize supported Bedrock Claude models"""
        self.supported_models = {
            'claude-3-haiku': ModelConfig(
                model_id='anthropic.claude-3-haiku-20240307-v1:0',
                display_name='Claude 3 Haiku',
                max_tokens=4096,
                supports_streaming=True
            ),
            'claude-3-sonnet': ModelConfig(
                model_id='anthropic.claude-3-sonnet-20240229-v1:0',
                display_name='Claude 3 Sonnet',
                max_tokens=4096,
                supports_streaming=True
            ),
            'claude-3-opus': ModelConfig(
                model_id='anthropic.claude-3-opus-20240229-v1:0',
                display_name='Claude 3 Opus',
                max_tokens=4096,
                supports_streaming=True
            ),
            'claude-3.5-sonnet': ModelConfig(
                model_id='anthropic.claude-3-5-sonnet-20240620-v1:0',
                display_name='Claude 3.5 Sonnet',
                max_tokens=8192,
                supports_streaming=True
            ),
            'claude-3.5-haiku': ModelConfig(
                model_id='anthropic.claude-3-5-haiku-20241022-v1:0',
                display_name='Claude 3.5 Haiku',
                max_tokens=8192,
                supports_streaming=True
            )
        }

    def _prepare_request(self, request: CompletionRequest) -> str:
        """Prepare Bedrock API request format"""
        messages = [{"role": msg.role, "content": msg.content} for msg in request.messages]

        body = {
            "anthropic_version": "bedrock-2023-05-31",
            "max_tokens": request.max_tokens,
            "messages": messages,
            "temperature": request.temperature,
            "top_p": request.top_p
        }

        # Add any additional parameters
        if request.additional_params:
            body.update(request.additional_params)

        return json.dumps(body)

    def _parse_response(self, response_data: Dict[str, Any], model: str) -> CompletionResponse:
        """Parse Bedrock response to unified format"""
        content = response_data['content'][0]['text']
        tokens_used = response_data.get('usage', {}).get('output_tokens', len(content.split()))
        stop_reason = response_data.get('stop_reason')

        return CompletionResponse(
            content=content,
            model=model,
            tokens_used=tokens_used,
            finish_reason=stop_reason,
            provider_specific_data=response_data
        )

    async def _invoke_model_async(self, model_id: str, body: str) -> Dict[str, Any]:
        """Async wrapper for synchronous Bedrock invoke_model call"""
        loop = asyncio.get_event_loop()
        response = await loop.run_in_executor(
            self.executor,
            lambda: self.bedrock_runtime.invoke_model(
                body=body,
                modelId=model_id,
                accept='application/json',
                contentType='application/json'
            )
        )
        return json.loads(response.get('body').read())

    async def _invoke_model_stream_async(self, model_id: str, body: str) -> AsyncGenerator[Dict[str, Any], None]:
        """Async wrapper for synchronous Bedrock invoke_model_with_response_stream call"""
        loop = asyncio.get_event_loop()
        queue: asyncio.Queue = asyncio.Queue()

        def _blocking_reader():
            try:
                response = self.bedrock_runtime.invoke_model_with_response_stream(
                    body=body,
                    modelId=model_id,
                    accept="application/json",
                    contentType="application/json",
                )
                stream = response.get("body")
                for event in stream:
                    chunk = event.get("chunk")
                    if chunk:
                        chunk_data = json.loads(chunk.get("bytes").decode())
                        # 把结果放到 async 队列里
                        asyncio.run_coroutine_threadsafe(queue.put(chunk_data), loop)
            except Exception as e:
                # 将异常也放入队列，由异步消费者处理
                asyncio.run_coroutine_threadsafe(queue.put(e), loop)
            finally:
                # 结束标记
                asyncio.run_coroutine_threadsafe(queue.put(None), loop)

        # 在后台线程里跑阻塞代码
        loop.run_in_executor(self.executor, _blocking_reader)
        # 从 queue 异步消费，返回给调用方
        while True:
            item = await queue.get()
            if item is None:  # 结束
                break
            if isinstance(item, Exception):
                raise item  # 把线程里捕获的异常重新抛给 async for 消费者
            yield item

    async def create_completion_stream(self, request: CompletionRequest) -> AsyncGenerator[str, None]:
        """Create streaming completion via Bedrock API"""
        if not self.is_model_supported(request.model):
            raise ValueError(f"Model {request.model} is not supported by Bedrock client")

        model_config = self.get_model_config(request.model)
        body = self._prepare_request(request)

        async for chunk_data in self._invoke_model_stream_async(model_config.model_id, body):
            if chunk_data.get('type') == 'content_block_delta':
                delta = chunk_data.get('delta', {})
                if 'text' in delta:
                    yield delta['text']

    async def create_completion_non_stream(self, request: CompletionRequest) -> CompletionResponse:
        """Create non-streaming completion via Bedrock API"""
        if not self.is_model_supported(request.model):
            raise ValueError(f"Model {request.model} is not supported by Bedrock client")

        model_config = self.get_model_config(request.model)
        body = self._prepare_request(request)

        response_data = await self._invoke_model_async(model_config.model_id, body)
        return self._parse_response(response_data, request.model)

    def __del__(self):
        """Cleanup executor on deletion"""
        if hasattr(self, 'executor'):
            self.executor.shutdown(wait=False)