#!/usr/bin/env python3
"""
Example Usage of Unified AI Testing Framework
Demonstrates how to use the unified architecture to test multiple AI providers
"""

import asyncio
import logging
import os
from typing import List, Optional

from openrouter_client import OpenRouterClient
from bedrock_client import BedrockClient
from unified_test_framework import UnifiedTestFramework

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def run_openrouter_only_test():
    """Example: Test only OpenRouter"""
    logger.info("Running OpenRouter-only test")
    
    # Initialize OpenRouter client
    api_key = os.getenv('OPENROUTER_API_KEY')
    if not api_key or api_key == 'your-api-key-here':
        logger.error("Please set OPENROUTER_API_KEY environment variable")
        return
    
    openrouter_client = OpenRouterClient(api_key)
    
    # Create test framework with single client
    framework = UnifiedTestFramework([openrouter_client])
    
    # Run comprehensive test
    results = await framework.run_comprehensive_test(
        models_to_test=['claude-3-haiku', 'claude-3-opus', 'claude-3-sonnet','claude-3.5-sonnet'],
        batch_size=10,
        test_both_modes=True
    )
    
    # Print results
    framework.print_detailed_results(results)
    
    # Generate and print comparison report
    report = framework.generate_comparison_report(results)
    print(report)
    
    # Save results
    filename = framework.save_results(results)
    logger.info(f"Results saved to: {filename}")

async def run_bedrock_only_test():
    """Example: Test only AWS Bedrock"""
    logger.info("Running Bedrock-only test")
    
    try:
        # Initialize Bedrock client (uses default AWS credentials)
        bedrock_client = BedrockClient(region_name='us-west-2')
        
        # Create test framework with single client
        framework = UnifiedTestFramework([bedrock_client])
        
        # Run comprehensive test with Bedrock models
        results = await framework.run_comprehensive_test(
            models_to_test=['claude-3-haiku', 'claude-3-opus', 'claude-3-sonnet','claude-3.5-sonnet'],
            batch_size=10,
            test_both_modes=True
        )
        
        # Print results
        framework.print_detailed_results(results)
        
        # Generate and print comparison report
        report = framework.generate_comparison_report(results)
        print(report)
        
        # Save results
        filename = framework.save_results(results)
        logger.info(f"Results saved to: {filename}")
        
    except Exception as e:
        logger.error(f"Bedrock test failed: {e}")
        logger.info("Make sure AWS credentials are configured and you have access to Bedrock")

async def run_cross_provider_comparison():
    """Example: Compare performance across multiple providers"""
    logger.info("Running cross-provider comparison test")
    
    clients = []
    
    # Add OpenRouter client if API key is available
    openrouter_api_key = os.getenv('OPENROUTER_API_KEY')
    if openrouter_api_key and openrouter_api_key != 'your-api-key-here':
        try:
            openrouter_client = OpenRouterClient(openrouter_api_key)
            clients.append(openrouter_client)
            logger.info("Added OpenRouter client")
        except Exception as e:
            logger.warning(f"Failed to initialize OpenRouter client: {e}")
    
    # Add Bedrock client if AWS credentials are available
    try:
        bedrock_client = BedrockClient(region_name='us-west-2')
        clients.append(bedrock_client)
        logger.info("Added Bedrock client")
    except Exception as e:
        logger.warning(f"Failed to initialize Bedrock client: {e}")
    
    if not clients:
        logger.error("No clients available. Please configure API keys and credentials.")
        return
    
    # Create unified test framework
    framework = UnifiedTestFramework(clients)
    
    # Get common models across all providers
    common_models = framework.get_common_models()
    logger.info(f"Common models found: {common_models}")
    
    if not common_models:
        logger.info("No common models found, testing all available models")
        all_models = framework.get_all_models_by_provider()
        logger.info(f"Available models by provider: {all_models}")
    
    # Run comprehensive test
    results = await framework.run_comprehensive_test(
        models_to_test=common_models if common_models else None,
        batch_size=10,
        test_both_modes=True
    )
    
    # Print detailed results
    framework.print_detailed_results(results)
    
    # Generate and print comparison report
    report = framework.generate_comparison_report(results)
    
    # Save results
    filename = framework.save_results(results)
    logger.info(f"Cross-provider comparison results saved to: {filename}")

async def run_custom_test():
    """Example: Run custom test with specific configuration"""
    logger.info("Running custom test configuration")
    
    # Initialize clients
    clients = []
    
    # OpenRouter with custom configuration
    openrouter_api_key = os.getenv('OPENROUTER_API_KEY')
    if openrouter_api_key and openrouter_api_key != 'your-api-key-here':
        openrouter_client = OpenRouterClient(openrouter_api_key)
        clients.append(openrouter_client)
    
    if not clients:
        logger.error("No clients available for custom test")
        return
    
    # Create framework
    framework = UnifiedTestFramework(clients)
    
    # Custom test configuration
    custom_models = ['anthropic/claude-3-haiku']  # Test only fast model
    
    # Run test with custom parameters
    results = await framework.run_comprehensive_test(
        models_to_test=custom_models,
        batch_size=5,  # Larger batch for better statistics
        test_both_modes=False  # Only streaming mode
    )
    
    # Print results
    framework.print_detailed_results(results)
    
    # Save results with custom filename
    filename = framework.save_results(results, "custom_test_results.json")
    logger.info(f"Custom test results saved to: {filename}")

async def main():
    """Main function to demonstrate different testing scenarios"""
    print("Unified AI Testing Framework - Example Usage")
    print("=" * 50)
    
    # Choose which test to run
    test_choice = input("""
Choose a test to run:
1. OpenRouter only
2. Bedrock only  
3. Cross-provider comparison
4. Custom test configuration
5. All tests (sequential)

Enter choice (1-5): """).strip()
    
    try:
        if test_choice == '1':
            await run_openrouter_only_test()
        elif test_choice == '2':
            await run_bedrock_only_test()
        elif test_choice == '3':
            await run_cross_provider_comparison()
        elif test_choice == '4':
            await run_custom_test()
        elif test_choice == '5':
            logger.info("Running all tests sequentially...")
            await run_openrouter_only_test()
            await asyncio.sleep(5)
            await run_bedrock_only_test()
            await asyncio.sleep(5)
            await run_cross_provider_comparison()
            await asyncio.sleep(5)
            await run_custom_test()
        else:
            logger.error("Invalid choice. Please run again and select 1-5.")
            
    except KeyboardInterrupt:
        logger.info("Test interrupted by user")
    except Exception as e:
        logger.error(f"Test failed with error: {e}")
        raise

if __name__ == "__main__":
    # Set environment variables if needed
    os.environ['OPENROUTER_API_KEY'] = '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'
    
    asyncio.run(main())
