package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"topnetwork.ai/topai/chat-webserver/analysis/api"
	"topnetwork.ai/topai/chat-webserver/analysis/repository/models"
	"topnetwork.ai/topai/chat-webserver/analysis/service"
)

func main() {
	// 初始化数据库连接
	db, err := initDatabase()
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}

	// 自动迁移数据库表
	if err := autoMigrate(db); err != nil {
		log.Fatalf("Failed to migrate database: %v", err)
	}

	// 创建分析管理器
	analysisManager := service.NewAnalysisManager(db)

	// 创建API处理器
	apiHandler := api.NewAnalysisAPIHandler(analysisManager)

	// 设置Gin路由
	r := gin.Default()

	// 添加中间件
	r.Use(corsMiddleware())
	r.Use(loggingMiddleware())

	// 注册路由
	apiHandler.RegisterRoutes(r)

	// 创建HTTP服务器
	srv := &http.Server{
		Addr:    ":8080",
		Handler: r,
	}

	// 启动分析服务
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	if err := analysisManager.StartAnalysis(ctx); err != nil {
		log.Printf("Failed to start analysis service: %v", err)
	} else {
		log.Println("Analysis service started successfully")
	}

	// 启动HTTP服务器
	go func() {
		log.Println("Starting HTTP server on :8080")
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Println("Shutting down server...")

	// 停止分析服务
	analysisManager.StopAnalysis()

	// 关闭HTTP服务器
	ctx, cancel = context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := srv.Shutdown(ctx); err != nil {
		log.Printf("Server forced to shutdown: %v", err)
	}

	log.Println("Server exited")
}

// initDatabase 初始化数据库连接
func initDatabase() (*gorm.DB, error) {
	// 从环境变量获取数据库配置
	dsn := os.Getenv("DATABASE_URL")
	if dsn == "" {
		// 默认配置
		dsn = "root:password@tcp(localhost:3306)/payment_analysis?charset=utf8mb4&parseTime=True&loc=Local"
	}

	// 配置GORM日志
	gormLogger := logger.New(
		log.New(os.Stdout, "\r\n", log.LstdFlags),
		logger.Config{
			SlowThreshold:             time.Second,
			LogLevel:                  logger.Info,
			IgnoreRecordNotFoundError: true,
			Colorful:                  true,
		},
	)

	// 连接数据库
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: gormLogger,
	})
	if err != nil {
		return nil, err
	}

	// 配置连接池
	sqlDB, err := db.DB()
	if err != nil {
		return nil, err
	}

	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)
	sqlDB.SetConnMaxLifetime(time.Hour)

	return db, nil
}

// autoMigrate 自动迁移数据库表
func autoMigrate(db *gorm.DB) error {
	log.Println("Starting database migration...")

	// 迁移所有模型
	err := db.AutoMigrate(
		&models.StripeEvent{},
		&models.PaymentEvent{},
		&models.UserPaymentDailyStats{},
		&models.PaymentFailureStats{},
		&models.UserCardUsage{},
		&models.GeoRiskConfig{},
	)

	if err != nil {
		return err
	}

	log.Println("Database migration completed successfully")
	return nil
}

// corsMiddleware CORS中间件
func corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	}
}

// loggingMiddleware 日志中间件
func loggingMiddleware() gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		return fmt.Sprintf("%s - [%s] \"%s %s %s %d %s \"%s\" %s\"\n",
			param.ClientIP,
			param.TimeStamp.Format(time.RFC1123),
			param.Method,
			param.Path,
			param.Request.Proto,
			param.StatusCode,
			param.Latency,
			param.Request.UserAgent(),
			param.ErrorMessage,
		)
	})
}

// 示例：如何使用分析功能
func exampleUsage() {
	// 这个函数展示了如何使用分析功能

	// 1. 初始化数据库
	db, err := initDatabase()
	if err != nil {
		log.Fatal(err)
	}

	// 2. 创建分析管理器
	analysisManager := service.NewAnalysisManager(db)

	// 3. 启动分析服务
	ctx := context.Background()
	if err := analysisManager.StartAnalysis(ctx); err != nil {
		log.Printf("Failed to start analysis: %v", err)
	}

	// 4. 获取用户分析报告
	userID := "cus_example123"
	hours := 24

	userReport, err := analysisManager.GetUserAnalysis(ctx, userID, hours)
	if err != nil {
		log.Printf("Failed to get user analysis: %v", err)
	} else {
		log.Printf("User %s analysis:", userID)
		log.Printf("  Total Payments: %d", userReport.PaymentAnalysis.TotalPayments)
		log.Printf("  Failure Rate: %.2f%%", userReport.PaymentAnalysis.PaymentFailureRate*100)
		log.Printf("  Dispute Rate: %.2f%%", userReport.PaymentAnalysis.DisputeRate*100)
		log.Printf("  Unique Cards: %d", userReport.PaymentAnalysis.UniqueCardsUsed)
		log.Printf("  Charge Frequency: %.2f per day", userReport.PaymentAnalysis.ChargeFrequency)
	}

	// 5. 获取系统分析报告
	systemReport, err := analysisManager.GetSystemAnalysis(ctx, hours)
	if err != nil {
		log.Printf("Failed to get system analysis: %v", err)
	} else {
		log.Printf("System analysis:")
		log.Printf("  Active Users: %d", systemReport.ActiveUsers)
		log.Printf("  Total Events: %d", systemReport.SystemMetrics.TotalEvents)
		log.Printf("  Success Rate: %.2f%%", systemReport.SystemMetrics.SuccessRate*100)
		log.Printf("  Failure Rate: %.2f%%", systemReport.SystemMetrics.FailureRate*100)
		log.Printf("  High Risk Users: %d", len(systemReport.HighRiskUsers))
	}

	// 6. 手动触发分析
	manualReport, err := analysisManager.ManualAnalysis(ctx, userID, hours)
	if err != nil {
		log.Printf("Failed to run manual analysis: %v", err)
	} else {
		log.Printf("Manual analysis completed for user %s", manualReport.UserID)
	}

	// 7. 停止分析服务
	analysisManager.StopAnalysis()
}
