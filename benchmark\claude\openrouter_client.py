#!/usr/bin/env python3
"""
OpenRouter AI Client Implementation
Implements the unified AI client interface for OpenRouter API
"""

import aiohttp
import json
import asyncio
from typing import List, Dict, Any, Optional, AsyncGenerator
import logging

try:
    from .base_ai_client import (
        BaseAIClient, CompletionRequest, CompletionResponse,
        ChatMessage, ModelConfig, TestResult
    )
except ImportError:
    from base_ai_client import (
        BaseAIClient, CompletionRequest, CompletionResponse,
        ChatMessage, ModelConfig, TestResult
    )

logger = logging.getLogger(__name__)

class OpenRouterClient(BaseAIClient):
    """OpenRouter API client implementation"""

    def __init__(self, api_key: str, base_url: str = "https://openrouter.ai/api/v1"):
        self.api_key = api_key
        self.base_url = base_url
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json",
        }
        super().__init__("openrouter")

    def _initialize_models(self) -> None:
        """Initialize supported OpenRouter models"""
        self.supported_models = {
            "claude-3.5-sonnet": ModelConfig(
                model_id="anthropic/claude-3.5-sonnet",
                display_name="Claude 3.5 Sonnet",
                max_tokens=8192,
                supports_streaming=True
            ),
            "claude-3-haiku": ModelConfig(
                model_id="anthropic/claude-3-haiku",
                display_name="Claude 3 Haiku",
                max_tokens=4096,
                supports_streaming=True
            ),
            "claude-3-opus": ModelConfig(
                model_id="anthropic/claude-3-opus",
                display_name="Claude 3 Opus",
                max_tokens=4096,
                supports_streaming=True
            ),
            "claude-3-sonnet": ModelConfig(
                model_id="anthropic/claude-3-sonnet",
                display_name="Claude 3 Sonnet",
                max_tokens=4096,
                supports_streaming=True
            )
        }

    def _prepare_request(self, request: CompletionRequest) -> Dict[str, Any]:
        """Prepare OpenRouter API request format"""
        messages = [{"role": msg.role, "content": msg.content} for msg in request.messages]

        model_config = self.get_model_config(request.model)

        payload = {
            "model": model_config.model_id,
            "messages": messages,
            "max_tokens": request.max_tokens,
            "temperature": request.temperature,
            "top_p": request.top_p,
            "stream": request.stream
        }

        # Add any additional parameters
        if request.additional_params:
            payload.update(request.additional_params)

        return payload

    def _parse_response(self, response_data: Dict[str, Any], model: str) -> CompletionResponse:
        """Parse OpenRouter response to unified format"""
        content = response_data['choices'][0]['message']['content']
        tokens_used = len(content.split())  # Approximate token count
        finish_reason = response_data['choices'][0].get('finish_reason')

        return CompletionResponse(
            content=content,
            model=model,
            tokens_used=tokens_used,
            finish_reason=finish_reason,
            provider_specific_data=response_data
        )

    async def create_completion_stream(self, request: CompletionRequest) -> AsyncGenerator[str, None]:
        """Create streaming completion via OpenRouter API"""
        if not self.is_model_supported(request.model):
            raise ValueError(f"Model {request.model} is not supported by OpenRouter client")

        request.stream = True
        payload = self._prepare_request(request)

        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{self.base_url}/chat/completions",
                headers=self.headers,
                json=payload
            ) as response:
            
                if response.status != 200:
                    error_text = await response.text()
                    raise Exception(f"HTTP {response.status}: {error_text}")

                async for line in response.content:
                    if line:
                        line_str = line.decode('utf-8').strip()
                        if line_str.startswith('data: '):
                            data_str = line_str[6:]
                            if data_str == '[DONE]':
                                break

                            try:
                                data = json.loads(data_str)
                                if 'choices' in data and data['choices']:
                                    delta = data['choices'][0].get('delta', {})
                                    if 'content' in delta:
                                        yield delta['content']
                            except json.JSONDecodeError as e:
                                logger.error(f"Failed to parse JSON: {e}")
                                continue

    async def create_completion_non_stream(self, request: CompletionRequest) -> CompletionResponse:
        """Create non-streaming completion via OpenRouter API"""
        if not self.is_model_supported(request.model):
            raise ValueError(f"Model {request.model} is not supported by OpenRouter client")

        request.stream = False
        payload = self._prepare_request(request)

        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{self.base_url}/chat/completions",
                headers=self.headers,
                json=payload
            ) as response:

                response_data = await response.json()

                if response.status != 200:
                    error_msg = response_data.get('error', {}).get('message', 'Unknown error')
                    raise Exception(f"HTTP {response.status}: {error_msg}")

                return self._parse_response(response_data, request.model)