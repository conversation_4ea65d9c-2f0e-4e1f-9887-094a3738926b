# Unified AI Testing Framework

A comprehensive testing framework for AI model performance comparison across multiple providers including OpenRouter and AWS Bedrock.

## Features

- **Unified Interface**: Abstract base class for consistent API across different AI providers
- **Multiple Providers**: Support for OpenRouter and AWS Bedrock with easy extensibility
- **Comprehensive Testing**: Both streaming and non-streaming performance testing
- **Performance Metrics**: Detailed statistics including response time, throughput, success rate, and percentiles
- **Cross-Provider Comparison**: Side-by-side performance comparison across different providers
- **Flexible Configuration**: Customizable test cases, batch sizes, and model selection
- **Detailed Reporting**: Rich console output and JSON export capabilities

## Architecture

### Core Components

1. **BaseAIClient**: Abstract base class defining the unified interface
2. **OpenRouterClient**: Implementation for OpenRouter API
3. **BedrockClient**: Implementation for AWS Bedrock API
4. **UnifiedTestFramework**: Comprehensive testing and comparison framework

### Data Structures

- **TestResult**: Individual test result with performance metrics
- **PerformanceMetrics**: Aggregated statistics for multiple test runs
- **ModelConfig**: Model configuration and capabilities
- **CompletionRequest/Response**: Unified request/response format

## Installation

### Dependencies

```bash
pip install aiohttp boto3 numpy
```

### Environment Setup

For OpenRouter:
```bash
export OPENROUTER_API_KEY="your-openrouter-api-key"
```

For AWS Bedrock:
```bash
export AWS_ACCESS_KEY_ID="your-aws-access-key"
export AWS_SECRET_ACCESS_KEY="your-aws-secret-key"
export AWS_DEFAULT_REGION="us-west-2"
```

## Quick Start

### Basic Usage

```python
import asyncio
from testutil import OpenRouterClient, BedrockClient, UnifiedTestFramework

async def main():
    # Initialize clients
    openrouter = OpenRouterClient("your-api-key")
    bedrock = BedrockClient(region_name="us-west-2")
    
    # Create testing framework
    framework = UnifiedTestFramework([openrouter, bedrock])
    
    # Run comprehensive test
    results = await framework.run_comprehensive_test(
        models_to_test=['claude-3-haiku'],
        batch_size=5,
        test_both_modes=True
    )
    
    # Print results and generate report
    framework.print_detailed_results(results)
    report = framework.generate_comparison_report(results)
    print(report)
    
    # Save results
    filename = framework.save_results(results)
    print(f"Results saved to: {filename}")

asyncio.run(main())
```

### Single Provider Testing

```python
# Test only OpenRouter
openrouter = OpenRouterClient("your-api-key")
framework = UnifiedTestFramework([openrouter])

results = await framework.run_comprehensive_test(
    models_to_test=['anthropic/claude-3.5-sonnet'],
    batch_size=3
)
```

### Custom Test Cases

```python
from testutil import ChatMessage, CompletionRequest

# Create custom test request
request = CompletionRequest(
    messages=[ChatMessage(role="user", content="Explain quantum computing")],
    model="claude-3-haiku",
    max_tokens=500,
    temperature=0.7
)

# Test single request
result = await client.test_streaming_performance(request)
print(f"Response time: {result.response_time:.2f}s")
print(f"Tokens generated: {result.tokens_generated}")
```

## Supported Models

### OpenRouter
- anthropic/claude-3.5-sonnet
- anthropic/claude-3-haiku
- anthropic/claude-3-opus
- anthropic/claude-3-sonnet

### AWS Bedrock
- claude-3-haiku
- claude-3-sonnet
- claude-3-opus
- claude-3.5-sonnet
- claude-3.5-haiku

## Test Cases

The framework includes predefined test cases covering:

1. **Simple Q&A**: Basic question answering
2. **Code Generation**: Programming tasks
3. **Text Analysis**: Sentiment and content analysis
4. **Creative Writing**: Story and content creation
5. **Data Analysis**: Analytical reasoning
6. **Complex Analysis**: Long-form comprehensive analysis

## Performance Metrics

- **Response Time**: Total time from request to completion
- **First Token Time**: Time to receive first token (streaming only)
- **Tokens per Second**: Throughput measurement
- **Success Rate**: Percentage of successful requests
- **Percentiles**: P50, P90, P95, P99 response times
- **Standard Deviation**: Response time variability

## Extending the Framework

### Adding New Providers

1. Create a new client class inheriting from `BaseAIClient`
2. Implement required abstract methods:
   - `_initialize_models()`
   - `create_completion_stream()`
   - `create_completion_non_stream()`
   - `_prepare_request()`
   - `_parse_response()`

```python
class NewProviderClient(BaseAIClient):
    def __init__(self, api_key: str):
        super().__init__("new_provider")
        self.api_key = api_key
    
    def _initialize_models(self):
        self.supported_models = {
            "model-name": ModelConfig(
                model_id="provider-model-id",
                display_name="Model Display Name",
                max_tokens=4096,
                supports_streaming=True
            )
        }
    
    # Implement other required methods...
```

## Example Scripts

- `example_unified_test.py`: Comprehensive usage examples
- `openrouter_test.py`: Original OpenRouter testing script
- `bedrock_test.py`: Original Bedrock testing script

## Output Files

- Test results are saved as JSON files with timestamp
- Detailed performance metrics and comparison data
- Easily parseable for further analysis or visualization

## Best Practices

1. **Rate Limiting**: Add delays between requests to avoid API limits
2. **Error Handling**: All methods include comprehensive error handling
3. **Async Operations**: Use async/await for better performance
4. **Batch Testing**: Use appropriate batch sizes for statistical significance
5. **Model Selection**: Test common models for fair comparison

## Troubleshooting

### Common Issues

1. **API Key Issues**: Ensure environment variables are set correctly
2. **AWS Credentials**: Configure AWS CLI or environment variables
3. **Rate Limiting**: Increase delays between requests if hitting limits
4. **Model Availability**: Check model availability in your region/account

### Logging

Enable detailed logging:
```python
import logging
logging.basicConfig(level=logging.INFO)
```

## License

This framework is designed for testing and comparison purposes. Ensure compliance with each provider's terms of service.
