# 支付审计系统

基于Stripe事件数据的支付审计和风险分析系统，提供实时的支付失败率、争议率、充值频率等关键指标分析。

## 功能特性

### 核心分析指标
1. **支付失败率分析** - 计算指定时间段内每个用户的支付失败率（失败次数/总支付次数）
2. **支付争议率分析** - 计算指定时间段内每个用户的支付争议率（争议次数/总支付次数）
3. **充值卡使用统计** - 统计指定时间段内每个用户使用的不同银行卡数量
4. **充值频率分析** - 计算指定时间段内每个用户的充值频率（充值次数/时间段）

### 失败原因详细分析
5. **充值失败原因分类** - 对每次充值失败进行详细分类，解析Stripe返回的信息：
   - 错误代码 (stripeErr.Code)
   - 拒付代码 (chargeback code)
   - 交易结果类型 (charge.Outcome.Type)

### 风险管理
6. **地理风险评估** - 检查充值银行卡的发卡国家是否符合公司风险管理政策要求

## 系统架构

```
├── analysis/
│   ├── api/                    # API接口层
│   │   ├── analysis_api.go     # 主要API接口
│   │   └── payment_audit.go    # 支付审计API（旧版）
│   ├── repository/             # 数据访问层
│   │   ├── models/             # 数据模型
│   │   │   └── payment_audit.go
│   │   ├── migrations/         # 数据库迁移
│   │   │   └── 0003_payment_audit_schema.sql
│   │   └── stripe_dao.go       # Stripe事件DAO
│   ├── service/                # 业务逻辑层
│   │   ├── analysis_manager.go      # 分析管理器
│   │   ├── payment_analysis_service.go # 支付分析服务
│   │   ├── scheduler_service.go     # 定时任务服务
│   │   ├── failure_analysis.go     # 失败原因分析
│   │   └── geo_risk.go             # 地理风险评估
│   └── main.go                 # 主启动文件
```

## 快速开始

### 1. 环境准备

确保已安装：
- Go 1.19+
- MySQL 8.0+
- 有效的Stripe事件数据

### 2. 数据库配置

设置环境变量：
```bash
export DATABASE_URL="root:password@tcp(localhost:3306)/payment_analysis?charset=utf8mb4&parseTime=True&loc=Local"
```

### 3. 启动服务

```bash
cd analysis
go mod tidy
go run main.go
```

服务将在 `http://localhost:8080` 启动

### 4. API使用示例

#### 获取用户分析报告
```bash
curl "http://localhost:8080/api/v1/analysis/users/cus_example123?hours=24"
```

#### 获取系统分析报告
```bash
curl "http://localhost:8080/api/v1/analysis/system?hours=24"
```

#### 手动触发用户分析
```bash
curl -X POST "http://localhost:8080/api/v1/analysis/users/cus_example123/manual?hours=24"
```

#### 检查服务状态
```bash
curl "http://localhost:8080/api/v1/analysis/status"
```

#### 分页查询Stripe事件
```bash
# 基本分页查询
curl "http://localhost:8080/api/v1/analysis/stripe-events?page=1&page_size=20"

# 按事件类型过滤
curl "http://localhost:8080/api/v1/analysis/stripe-events?event_types=payment_intent.succeeded&event_types=charge.succeeded"

# 按时间范围过滤
curl "http://localhost:8080/api/v1/analysis/stripe-events?start_time=2024-01-01T00:00:00Z&end_time=2024-01-02T00:00:00Z"

# 查询未处理事件
curl "http://localhost:8080/api/v1/analysis/stripe-events?processed=false"

# 查询特定用户事件
curl "http://localhost:8080/api/v1/analysis/users/cus_example123/stripe-events?page=1&page_size=10"

# 组合过滤条件
curl "http://localhost:8080/api/v1/analysis/stripe-events?customer_id=cus_example123&event_types=payment_intent.payment_failed&processed=false"
```

## API接口文档

### 用户分析接口

#### GET /api/v1/analysis/users/{user_id}
获取指定用户的完整支付分析报告

**参数：**
- `user_id` (path): 用户ID
- `hours` (query): 分析时间窗口（小时），默认24

**响应示例：**
```json
{
  "user_id": "cus_example123",
  "time_range": {
    "start_time": "2024-01-01T00:00:00Z",
    "end_time": "2024-01-02T00:00:00Z"
  },
  "payment_analysis": {
    "total_payments": 10,
    "successful_payments": 8,
    "failed_payments": 2,
    "payment_failure_rate": 0.2,
    "dispute_rate": 0.1,
    "charge_frequency": 0.42,
    "unique_cards_used": 2,
    "failure_reasons": {
      "insufficient_funds": 1,
      "card_declined": 1
    },
    "card_details": [
      {
        "card_brand": "visa",
        "card_last4": "4242",
        "card_country": "US",
        "usage_count": 8,
        "success_count": 7,
        "fail_count": 1
      }
    ]
  },
  "failure_analysis": {
    "total_failures": 2,
    "categorized_failures": {
      "card_issues_declined": {
        "count": 1,
        "percentage": 50.0,
        "category": {
          "category": "card_issues",
          "sub_category": "declined",
          "description": "Card was declined by the issuing bank",
          "severity": "medium",
          "actionable": true
        }
      }
    },
    "recommendations": [
      "建议提醒用户检查银行卡信息，包括有效期和余额"
    ]
  },
  "geo_risk_assessment": {
    "country_analysis": [
      {
        "country_code": "US",
        "country_name": "United States",
        "risk_level": "low",
        "is_allowed": true,
        "payment_count": 10,
        "success_rate": 0.8,
        "failure_rate": 0.2
      }
    ],
    "risk_summary": {
      "total_countries": 1,
      "low_risk_countries": 1,
      "overall_risk_score": 1.0,
      "risk_level": "low"
    }
  },
  "generated_at": "2024-01-02T12:00:00Z"
}
```

### 系统分析接口

#### GET /api/v1/analysis/system
获取系统整体的支付分析报告

**参数：**
- `hours` (query): 分析时间窗口（小时），默认24

### 服务管理接口

#### GET /api/v1/analysis/status
获取分析服务运行状态

#### POST /api/v1/analysis/start
启动定时分析服务

#### POST /api/v1/analysis/stop
停止定时分析服务

#### GET /api/v1/analysis/health
健康检查

### Stripe事件查询接口

#### GET /api/v1/analysis/stripe-events
分页查询Stripe事件

**参数：**
- `page` (query): 页码，默认1
- `page_size` (query): 每页大小，默认20，最大100
- `event_types` (query): 事件类型过滤，可多选
- `start_time` (query): 开始时间，RFC3339格式
- `end_time` (query): 结束时间，RFC3339格式
- `processed` (query): 处理状态过滤
- `customer_id` (query): 客户ID过滤
- `event_id_contains` (query): 事件ID包含过滤

**响应示例：**
```json
{
  "events": [
    {
      "id": 1,
      "event_id": "evt_test_001",
      "event_type": "payment_intent.succeeded",
      "payload": "{...}",
      "processed": true,
      "created_at_stripe": "2024-01-01T12:00:00Z"
    }
  ],
  "pagination": {
    "total": 150,
    "page": 1,
    "page_size": 20,
    "total_pages": 8,
    "has_next": true,
    "has_previous": false
  }
}
```

#### GET /api/v1/analysis/users/{user_id}/stripe-events
分页查询用户Stripe事件

**参数：**
- `user_id` (path): 用户ID
- 其他参数同上

#### GET /api/v1/analysis/stripe-events/unprocessed
分页查询未处理的Stripe事件

**参数：**
- `page` (query): 页码，默认1
- `page_size` (query): 每页大小，默认20

## 定时任务配置

系统支持自动定时分析，默认配置：
- 分析间隔：15分钟
- 时间窗口：6小时
- 批处理大小：50个事件

可以通过修改 `service.DefaultAnalysisConfig()` 来调整配置。

## 数据库表结构

### 主要表
- `stripe_events` - Stripe事件存储
- `payment_events` - 支付事件详细记录
- `user_payment_daily_stats` - 用户支付日统计
- `payment_failure_stats` - 支付失败原因统计
- `user_card_usage` - 用户银行卡使用记录
- `geo_risk_config` - 地理风险配置

## 监控和日志

系统提供详细的日志记录：
- 分析任务执行日志
- 用户风险评估日志
- 系统性能指标日志
- 错误和异常日志

## 扩展和定制

### 添加新的分析指标
1. 在 `service/payment_analysis_service.go` 中添加新的分析逻辑
2. 更新数据模型和API响应结构
3. 添加相应的测试用例

### 自定义风险评估规则
1. 修改 `service/geo_risk.go` 中的风险评估逻辑
2. 更新 `geo_risk_config` 表的配置
3. 调整风险评分算法

## 故障排除

### 常见问题
1. **数据库连接失败** - 检查DATABASE_URL环境变量和数据库服务状态
2. **分析结果为空** - 确认Stripe事件数据已正确存储在数据库中
3. **性能问题** - 检查数据库索引和查询优化

### 日志查看
```bash
# 查看服务日志
tail -f /var/log/payment-analysis.log

# 查看错误日志
grep "ERROR" /var/log/payment-analysis.log
```

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License
