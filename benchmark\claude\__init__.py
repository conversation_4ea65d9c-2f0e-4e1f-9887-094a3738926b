"""
Unified AI Testing Framework
A comprehensive testing framework for AI model performance comparison across multiple providers
"""

from .base_ai_client import (
    BaseAIClient,
    TestResult,
    PerformanceMetrics,
    ModelConfig,
    ChatMessage,
    CompletionRequest,
    CompletionResponse
)

from .openrouter_client import OpenRouterClient
from .bedrock_client import BedrockClient
from .unified_test_framework import UnifiedTestFramework

__version__ = "1.0.0"
__author__ = "AI Testing Framework Team"

__all__ = [
    # Base classes and data structures
    'BaseAIClient',
    'TestResult',
    'PerformanceMetrics',
    'ModelConfig',
    'ChatMessage',
    'CompletionRequest',
    'CompletionResponse',
    
    # Client implementations
    'OpenRouterClient',
    'BedrockClient',
    
    # Testing framework
    'UnifiedTestFramework'
]
