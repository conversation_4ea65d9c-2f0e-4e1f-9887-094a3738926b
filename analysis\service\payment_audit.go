package service

import (
	"context"
	"fmt"
	"time"

	"gorm.io/gorm/clause"
	"topnetwork.ai/topai/chat-webserver/analysis/repository"
	"topnetwork.ai/topai/chat-webserver/analysis/repository/models"
)

// PaymentAuditService 支付审计服务
type PaymentAuditService struct {
	db *repository.DB
}

// NewPaymentAuditService 创建支付审计服务实例
func NewPaymentAuditService(db *repository.DB) *PaymentAuditService {
	return &PaymentAuditService{
		db: db,
	}
}

// TimeRange 时间范围结构
type TimeRange struct {
	StartTime time.Time `json:"start_time"`
	EndTime   time.Time `json:"end_time"`
}

// PaymentAnalysisResult 支付分析结果
type PaymentAnalysisResult struct {
	UserID              string         `json:"user_id"`
	TimeRange           TimeRange      `json:"time_range"`
	PaymentFailureRate  float64        `json:"payment_failure_rate"`
	DisputeRate         float64        `json:"dispute_rate"`
	ChargeFrequency     float64        `json:"charge_frequency"`
	UniqueCardsUsed     int            `json:"unique_cards_used"`
	TotalPayments       int            `json:"total_payments"`
	SuccessfulPayments  int            `json:"successful_payments"`
	FailedPayments      int            `json:"failed_payments"`
	DisputedPayments    int            `json:"disputed_payments"`
	RefundedPayments    int            `json:"refunded_payments"`
	TotalAmount         float64        `json:"total_amount"`
	SuccessfulAmount    float64        `json:"successful_amount"`
	FailureReasons      map[string]int `json:"failure_reasons"`
	CountryDistribution map[string]int `json:"country_distribution"`
	RiskFlags           []string       `json:"risk_flags"`
}

// SystemAnalysisResult 系统级分析结果
type SystemAnalysisResult struct {
	TimeRange          TimeRange           `json:"time_range"`
	TotalUsers         int                 `json:"total_users"`
	TotalPayments      int                 `json:"total_payments"`
	OverallFailureRate float64             `json:"overall_failure_rate"`
	OverallDisputeRate float64             `json:"overall_dispute_rate"`
	TopFailureReasons  []FailureReasonStat `json:"top_failure_reasons"`
	CountryRiskStats   []CountryRiskStat   `json:"country_risk_stats"`
	HighRiskUsers      []UserRiskSummary   `json:"high_risk_users"`
}

// FailureReasonStat 失败原因统计
type FailureReasonStat struct {
	Reason string  `json:"reason"`
	Count  int     `json:"count"`
	Rate   float64 `json:"rate"`
}

// CountryRiskStat 国家风险统计
type CountryRiskStat struct {
	Country      string  `json:"country"`
	RiskLevel    string  `json:"risk_level"`
	PaymentCount int     `json:"payment_count"`
	FailureRate  float64 `json:"failure_rate"`
}

// UserRiskSummary 用户风险摘要
type UserRiskSummary struct {
	UserID        string  `json:"user_id"`
	RiskScore     float64 `json:"risk_score"`
	FailureRate   float64 `json:"failure_rate"`
	DisputeRate   float64 `json:"dispute_rate"`
	TotalPayments int     `json:"total_payments"`
}

// AnalyzeUserPayments 分析用户支付数据
func (s *PaymentAuditService) AnalyzeUserPayments(ctx context.Context, userID string, timeRange TimeRange) (*PaymentAnalysisResult, error) {
	result := &PaymentAnalysisResult{
		UserID:              userID,
		TimeRange:           timeRange,
		FailureReasons:      make(map[string]int),
		CountryDistribution: make(map[string]int),
		RiskFlags:           []string{},
	}

	// 获取用户支付事件
	var events []models.PaymentEvent
	err := s.db.GetDB().Where("user_id = ? AND created_at BETWEEN ? AND ?",
		userID, timeRange.StartTime, timeRange.EndTime).Find(&events).Error
	if err != nil {
		return nil, fmt.Errorf("failed to fetch payment events: %w", err)
	}

	// 统计基础数据
	cardFingerprints := make(map[string]bool)
	countries := make(map[string]int)

	for _, event := range events {
		result.TotalPayments++

		switch event.EventType {
		case models.PaymentSucceeded:
			result.SuccessfulPayments++
			if event.Amount != nil {
				result.SuccessfulAmount += *event.Amount
			}
		case models.PaymentFailed:
			result.FailedPayments++
			if event.FailureCode != nil {
				result.FailureReasons[*event.FailureCode]++
			}
			if event.OutcomeType != nil {
				result.FailureReasons["outcome_"+*event.OutcomeType]++
			}
		case models.DisputeCreated:
			result.DisputedPayments++
			if event.DisputeReason != nil {
				result.FailureReasons["dispute_"+*event.DisputeReason]++
			}
		case models.RefundCreated:
			result.RefundedPayments++
		}

		if event.Amount != nil {
			result.TotalAmount += *event.Amount
		}

		// 统计银行卡使用
		if event.CardLast4 != nil && event.CardBrand != nil {
			fingerprint := fmt.Sprintf("%s_%s", *event.CardBrand, *event.CardLast4)
			cardFingerprints[fingerprint] = true
		}

		// 统计国家分布
		if event.CardCountry != nil {
			countries[*event.CardCountry]++
		}
	}

	result.UniqueCardsUsed = len(cardFingerprints)
	result.CountryDistribution = countries

	// 计算比率
	if result.TotalPayments > 0 {
		result.PaymentFailureRate = float64(result.FailedPayments) / float64(result.TotalPayments)
		result.DisputeRate = float64(result.DisputedPayments) / float64(result.TotalPayments)

		// 计算充值频率（次数/天）
		days := timeRange.EndTime.Sub(timeRange.StartTime).Hours() / 24
		if days > 0 {
			result.ChargeFrequency = float64(result.SuccessfulPayments) / days
		}
	}

	// 风险评估
	result.RiskFlags = s.assessRiskFlags(result)

	return result, nil
}

// AnalyzeSystemPayments 分析系统级支付数据
func (s *PaymentAuditService) AnalyzeSystemPayments(ctx context.Context, timeRange TimeRange) (*SystemAnalysisResult, error) {
	result := &SystemAnalysisResult{
		TimeRange:         timeRange,
		TopFailureReasons: []FailureReasonStat{},
		CountryRiskStats:  []CountryRiskStat{},
		HighRiskUsers:     []UserRiskSummary{},
	}

	// 获取系统级统计
	var totalStats struct {
		TotalUsers       int64
		TotalPayments    int64
		FailedPayments   int64
		DisputedPayments int64
	}

	err := s.db.GetDB().Model(&models.PaymentEvent{}).
		Select("COUNT(DISTINCT user_id) as total_users, COUNT(*) as total_payments, "+
			"SUM(CASE WHEN event_type = 'payment_failed' THEN 1 ELSE 0 END) as failed_payments, "+
			"SUM(CASE WHEN event_type = 'dispute_created' THEN 1 ELSE 0 END) as disputed_payments").
		Where("created_at BETWEEN ? AND ?", timeRange.StartTime, timeRange.EndTime).
		Scan(&totalStats).Error
	if err != nil {
		return nil, fmt.Errorf("failed to fetch system stats: %w", err)
	}

	result.TotalUsers = int(totalStats.TotalUsers)
	result.TotalPayments = int(totalStats.TotalPayments)

	if result.TotalPayments > 0 {
		result.OverallFailureRate = float64(totalStats.FailedPayments) / float64(result.TotalPayments)
		result.OverallDisputeRate = float64(totalStats.DisputedPayments) / float64(result.TotalPayments)
	}

	// 获取失败原因统计
	result.TopFailureReasons, err = s.getTopFailureReasons(ctx, timeRange, 10)
	if err != nil {
		return nil, fmt.Errorf("failed to get failure reasons: %w", err)
	}

	// 获取国家风险统计
	result.CountryRiskStats, err = s.getCountryRiskStats(ctx, timeRange)
	if err != nil {
		return nil, fmt.Errorf("failed to get country risk stats: %w", err)
	}

	// 获取高风险用户
	result.HighRiskUsers, err = s.getHighRiskUsers(ctx, timeRange, 20)
	if err != nil {
		return nil, fmt.Errorf("failed to get high risk users: %w", err)
	}

	return result, nil
}

// GetUserCardStats 获取用户银行卡使用统计
func (s *PaymentAuditService) GetUserCardStats(ctx context.Context, userID string, timeRange TimeRange) ([]models.UserCardUsage, error) {
	var cardStats []models.UserCardUsage
	err := s.db.GetDB().Where("user_id = ? AND last_used_at BETWEEN ? AND ?",
		userID, timeRange.StartTime, timeRange.EndTime).Find(&cardStats).Error
	if err != nil {
		return nil, fmt.Errorf("failed to fetch card stats: %w", err)
	}
	return cardStats, nil
}

// GetPaymentFrequencyAnalysis 获取支付频率分析
func (s *PaymentAuditService) GetPaymentFrequencyAnalysis(ctx context.Context, userID string, timeRange TimeRange, interval string) (map[string]int, error) {
	var results []struct {
		Period string
		Count  int
	}

	var groupBy string
	switch interval {
	case "hour":
		groupBy = "DATE_FORMAT(created_at, '%Y-%m-%d %H:00:00')"
	case "day":
		groupBy = "DATE(created_at)"
	case "week":
		groupBy = "YEARWEEK(created_at)"
	case "month":
		groupBy = "DATE_FORMAT(created_at, '%Y-%m')"
	default:
		groupBy = "DATE(created_at)"
	}

	err := s.db.GetDB().Model(&models.PaymentEvent{}).
		Select(fmt.Sprintf("%s as period, COUNT(*) as count", groupBy)).
		Where("user_id = ? AND created_at BETWEEN ? AND ? AND event_type = ?",
			userID, timeRange.StartTime, timeRange.EndTime, models.PaymentSucceeded).
		Group("period").
		Order("period").
		Scan(&results).Error
	if err != nil {
		return nil, fmt.Errorf("failed to analyze payment frequency: %w", err)
	}

	frequency := make(map[string]int)
	for _, result := range results {
		frequency[result.Period] = result.Count
	}

	return frequency, nil
}

// assessRiskFlags 评估风险标记
func (s *PaymentAuditService) assessRiskFlags(result *PaymentAnalysisResult) []string {
	var flags []string

	// 高失败率风险
	if result.PaymentFailureRate > 0.3 {
		flags = append(flags, "high_failure_rate")
	}

	// 高争议率风险
	if result.DisputeRate > 0.1 {
		flags = append(flags, "high_dispute_rate")
	}

	// 频繁充值风险
	if result.ChargeFrequency > 10 {
		flags = append(flags, "frequent_charges")
	}

	// 多卡使用风险
	if result.UniqueCardsUsed > 5 {
		flags = append(flags, "multiple_cards")
	}

	// 检查高风险国家
	for country := range result.CountryDistribution {
		var riskConfig models.GeoRiskConfig
		err := s.db.GetDB().Where("country_code = ?", country).First(&riskConfig).Error
		if err == nil && (riskConfig.RiskLevel == models.RiskLevelHigh || !riskConfig.IsAllowed) {
			flags = append(flags, fmt.Sprintf("high_risk_country_%s", country))
		}
	}

	return flags
}

// getTopFailureReasons 获取主要失败原因
func (s *PaymentAuditService) getTopFailureReasons(ctx context.Context, timeRange TimeRange, limit int) ([]FailureReasonStat, error) {
	var results []struct {
		FailureValue string
		Count        int64
	}

	err := s.db.GetDB().Model(&models.PaymentFailureStats{}).
		Select("failure_value, SUM(count) as count").
		Where("last_occurred_at BETWEEN ? AND ?", timeRange.StartTime, timeRange.EndTime).
		Group("failure_value").
		Order("count DESC").
		Limit(limit).
		Scan(&results).Error
	if err != nil {
		return nil, err
	}

	// 获取总失败次数用于计算比率
	var totalFailures int64
	s.db.GetDB().Model(&models.PaymentEvent{}).
		Where("created_at BETWEEN ? AND ? AND event_type IN ?",
			timeRange.StartTime, timeRange.EndTime,
			[]models.PaymentEventType{models.PaymentFailed, models.DisputeCreated}).
		Count(&totalFailures)

	var stats []FailureReasonStat
	for _, result := range results {
		rate := float64(0)
		if totalFailures > 0 {
			rate = float64(result.Count) / float64(totalFailures)
		}
		stats = append(stats, FailureReasonStat{
			Reason: result.FailureValue,
			Count:  int(result.Count),
			Rate:   rate,
		})
	}

	return stats, nil
}

// getCountryRiskStats 获取国家风险统计
func (s *PaymentAuditService) getCountryRiskStats(ctx context.Context, timeRange TimeRange) ([]CountryRiskStat, error) {
	var results []struct {
		Country      string
		PaymentCount int64
		FailedCount  int64
	}

	err := s.db.GetDB().Model(&models.PaymentEvent{}).
		Select("card_country as country, COUNT(*) as payment_count, "+
			"SUM(CASE WHEN event_type = 'payment_failed' THEN 1 ELSE 0 END) as failed_count").
		Where("created_at BETWEEN ? AND ? AND card_country IS NOT NULL",
			timeRange.StartTime, timeRange.EndTime).
		Group("card_country").
		Order("payment_count DESC").
		Scan(&results).Error
	if err != nil {
		return nil, err
	}

	var stats []CountryRiskStat
	for _, result := range results {
		failureRate := float64(0)
		if result.PaymentCount > 0 {
			failureRate = float64(result.FailedCount) / float64(result.PaymentCount)
		}

		// 获取风险等级
		var riskConfig models.GeoRiskConfig
		riskLevel := "unknown"
		err := s.db.GetDB().Where("country_code = ?", result.Country).First(&riskConfig).Error
		if err == nil {
			riskLevel = string(riskConfig.RiskLevel)
		}

		stats = append(stats, CountryRiskStat{
			Country:      result.Country,
			RiskLevel:    riskLevel,
			PaymentCount: int(result.PaymentCount),
			FailureRate:  failureRate,
		})
	}

	return stats, nil
}

// getHighRiskUsers 获取高风险用户
func (s *PaymentAuditService) getHighRiskUsers(ctx context.Context, timeRange TimeRange, limit int) ([]UserRiskSummary, error) {
	var results []struct {
		UserID           string
		TotalPayments    int64
		FailedPayments   int64
		DisputedPayments int64
	}

	err := s.db.GetDB().Model(&models.PaymentEvent{}).
		Select("user_id, COUNT(*) as total_payments, "+
			"SUM(CASE WHEN event_type = 'payment_failed' THEN 1 ELSE 0 END) as failed_payments, "+
			"SUM(CASE WHEN event_type = 'dispute_created' THEN 1 ELSE 0 END) as disputed_payments").
		Where("created_at BETWEEN ? AND ?", timeRange.StartTime, timeRange.EndTime).
		Group("user_id").
		Having("total_payments >= 5"). // 至少5次支付才考虑
		Order("(failed_payments + disputed_payments * 2) / total_payments DESC").
		Limit(limit).
		Scan(&results).Error
	if err != nil {
		return nil, err
	}

	var users []UserRiskSummary
	for _, result := range results {
		failureRate := float64(0)
		disputeRate := float64(0)
		if result.TotalPayments > 0 {
			failureRate = float64(result.FailedPayments) / float64(result.TotalPayments)
			disputeRate = float64(result.DisputedPayments) / float64(result.TotalPayments)
		}

		// 计算风险评分 (失败率 * 30% + 争议率 * 70%)
		riskScore := failureRate*0.3 + disputeRate*0.7

		users = append(users, UserRiskSummary{
			UserID:        result.UserID,
			RiskScore:     riskScore,
			FailureRate:   failureRate,
			DisputeRate:   disputeRate,
			TotalPayments: int(result.TotalPayments),
		})
	}

	return users, nil
}

// UpdateDailyStats 更新用户日统计数据
func (s *PaymentAuditService) UpdateDailyStats(ctx context.Context, userID string, date time.Time) error {
	startOfDay := time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, date.Location())
	endOfDay := startOfDay.Add(24 * time.Hour)

	// 获取当日支付事件
	var events []models.PaymentEvent
	err := s.db.GetDB().Where("user_id = ? AND created_at BETWEEN ? AND ?",
		userID, startOfDay, endOfDay).Find(&events).Error
	if err != nil {
		return fmt.Errorf("failed to fetch daily events: %w", err)
	}

	// 计算统计数据
	stats := models.UserPaymentDailyStats{
		UserID:   userID,
		StatDate: startOfDay,
	}

	cardFingerprints := make(map[string]bool)
	countries := make(map[string]bool)

	for _, event := range events {
		stats.TotalPayments++

		switch event.EventType {
		case models.PaymentSucceeded:
			stats.SuccessfulPayments++
			if event.Amount != nil {
				stats.SuccessfulAmount += *event.Amount
			}
		case models.PaymentFailed:
			stats.FailedPayments++
		case models.DisputeCreated:
			stats.DisputedPayments++
		case models.RefundCreated:
			stats.RefundedPayments++
		}

		if event.Amount != nil {
			stats.TotalAmount += *event.Amount
		}

		// 统计唯一银行卡
		if event.CardLast4 != nil && event.CardBrand != nil {
			fingerprint := fmt.Sprintf("%s_%s", *event.CardBrand, *event.CardLast4)
			cardFingerprints[fingerprint] = true
		}

		// 统计唯一国家
		if event.CardCountry != nil {
			countries[*event.CardCountry] = true
		}
	}

	stats.UniqueCardsUsed = len(cardFingerprints)
	stats.UniqueCountries = len(countries)

	// 使用UPSERT更新或插入统计数据
	return s.db.GetDB().Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "user_id"}, {Name: "stat_date"}},
		DoUpdates: clause.AssignmentColumns([]string{"total_payments", "successful_payments", "failed_payments", "disputed_payments", "refunded_payments", "total_amount", "successful_amount", "unique_cards_used", "unique_countries", "updated_at"}),
	}).Create(&stats).Error
}
