package service

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"sync"
	"time"

	"gorm.io/gorm"
	"topnetwork.ai/topai/chat-webserver/analysis/repository"
	"topnetwork.ai/topai/chat-webserver/analysis/repository/models"
)

// SchedulerService 定时任务服务
type SchedulerService struct {
	db                     *gorm.DB
	stripeEventDAO         *repository.StripeEventDAO
	paymentAnalysisService *PaymentAnalysisService
	isRunning              bool
	stopChan               chan struct{}
	wg                     sync.WaitGroup
	mu                     sync.RWMutex
}

// NewSchedulerService 创建定时任务服务实例
func NewSchedulerService(db *gorm.DB) *SchedulerService {
	return &SchedulerService{
		db:                     db,
		stripeEventDAO:         repository.NewStripeEventDAO(db),
		paymentAnalysisService: NewPaymentAnalysisService(db),
		stopChan:               make(chan struct{}),
	}
}

// AnalysisConfig 分析配置
type AnalysisConfig struct {
	BatchSize            int           `json:"batch_size"`             // 每批处理的事件数量
	AnalysisInterval     time.Duration `json:"analysis_interval"`      // 分析间隔
	TimeWindowHours      int           `json:"time_window_hours"`      // 分析时间窗口（小时）
	EnableUserAnalysis   bool          `json:"enable_user_analysis"`   // 是否启用用户级分析
	EnableSystemAnalysis bool          `json:"enable_system_analysis"` // 是否启用系统级分析
}

// DefaultAnalysisConfig 默认分析配置
func DefaultAnalysisConfig() AnalysisConfig {
	return AnalysisConfig{
		BatchSize:            100,
		AnalysisInterval:     30 * time.Minute, // 每30分钟分析一次
		TimeWindowHours:      24,               // 分析过去24小时的数据
		EnableUserAnalysis:   true,
		EnableSystemAnalysis: true,
	}
}

// Start 启动定时任务
func (s *SchedulerService) Start(ctx context.Context, config AnalysisConfig) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.isRunning {
		return fmt.Errorf("scheduler is already running")
	}

	s.isRunning = true
	log.Println("Starting payment analysis scheduler...")

	// 启动定时分析任务
	s.wg.Add(1)
	go s.runAnalysisScheduler(ctx, config)

	// 启动事件处理任务
	s.wg.Add(1)
	go s.runEventProcessor(ctx, config)

	return nil
}

// Stop 停止定时任务
func (s *SchedulerService) Stop() {
	s.mu.Lock()
	defer s.mu.Unlock()

	if !s.isRunning {
		return
	}

	log.Println("Stopping payment analysis scheduler...")
	close(s.stopChan)
	s.wg.Wait()
	s.isRunning = false
	log.Println("Payment analysis scheduler stopped")
}

// runAnalysisScheduler 运行分析调度器
func (s *SchedulerService) runAnalysisScheduler(ctx context.Context, config AnalysisConfig) {
	defer s.wg.Done()

	ticker := time.NewTicker(config.AnalysisInterval)
	defer ticker.Stop()

	// 立即执行一次分析
	s.performAnalysis(ctx, config)

	for {
		select {
		case <-ctx.Done():
			log.Println("Analysis scheduler stopped due to context cancellation")
			return
		case <-s.stopChan:
			log.Println("Analysis scheduler stopped")
			return
		case <-ticker.C:
			s.performAnalysis(ctx, config)
		}
	}
}

// runEventProcessor 运行事件处理器
func (s *SchedulerService) runEventProcessor(ctx context.Context, config AnalysisConfig) {
	defer s.wg.Done()

	ticker := time.NewTicker(5 * time.Minute) // 每5分钟处理一次未处理的事件
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			log.Println("Event processor stopped due to context cancellation")
			return
		case <-s.stopChan:
			log.Println("Event processor stopped")
			return
		case <-ticker.C:
			s.processUnhandledEvents(ctx, config)
		}
	}
}

// performAnalysis 执行分析
func (s *SchedulerService) performAnalysis(ctx context.Context, config AnalysisConfig) {
	log.Println("Starting payment analysis...")

	// 定义分析时间范围
	endTime := time.Now()
	startTime := endTime.Add(-time.Duration(config.TimeWindowHours) * time.Hour)
	timeRange := TimeRange{
		StartTime: startTime,
		EndTime:   endTime,
	}

	// 系统级分析
	if config.EnableSystemAnalysis {
		if err := s.performSystemAnalysis(ctx, timeRange); err != nil {
			log.Printf("System analysis failed: %v", err)
		}
	}

	// 用户级分析
	if config.EnableUserAnalysis {
		if err := s.performUserAnalysis(ctx, timeRange, config.BatchSize); err != nil {
			log.Printf("User analysis failed: %v", err)
		}
	}

	log.Println("Payment analysis completed")
}

// performSystemAnalysis 执行系统级分析
func (s *SchedulerService) performSystemAnalysis(ctx context.Context, timeRange TimeRange) error {
	log.Println("Performing system-level analysis...")

	// 获取事件类型统计
	eventCounts, err := s.stripeEventDAO.GetEventCountByType(timeRange.StartTime, timeRange.EndTime)
	if err != nil {
		return fmt.Errorf("failed to get event counts: %w", err)
	}

	// 计算系统级指标
	systemMetrics := s.calculateSystemMetrics(eventCounts)

	// 记录系统级分析结果
	log.Printf("System Analysis Results for %s to %s:",
		timeRange.StartTime.Format("2006-01-02 15:04:05"),
		timeRange.EndTime.Format("2006-01-02 15:04:05"))
	log.Printf("Total Events: %d", systemMetrics.TotalEvents)
	log.Printf("Payment Success Rate: %.2f%%", systemMetrics.SuccessRate*100)
	log.Printf("Payment Failure Rate: %.2f%%", systemMetrics.FailureRate*100)
	log.Printf("Dispute Rate: %.2f%%", systemMetrics.DisputeRate*100)

	// 这里可以将结果存储到数据库或发送到监控系统
	return nil
}

// SystemMetrics 系统指标
type SystemMetrics struct {
	TotalEvents int64            `json:"total_events"`
	SuccessRate float64          `json:"success_rate"`
	FailureRate float64          `json:"failure_rate"`
	DisputeRate float64          `json:"dispute_rate"`
	EventCounts map[string]int64 `json:"event_counts"`
}

// calculateSystemMetrics 计算系统指标
func (s *SchedulerService) calculateSystemMetrics(eventCounts map[string]int64) SystemMetrics {
	var totalEvents int64
	var successEvents int64
	var failureEvents int64
	var disputeEvents int64

	for eventType, count := range eventCounts {
		totalEvents += count

		switch {
		case contains(eventType, "succeeded"):
			successEvents += count
		case contains(eventType, "failed"):
			failureEvents += count
		case contains(eventType, "dispute"):
			disputeEvents += count
		}
	}

	metrics := SystemMetrics{
		TotalEvents: totalEvents,
		EventCounts: eventCounts,
	}

	if totalEvents > 0 {
		metrics.SuccessRate = float64(successEvents) / float64(totalEvents)
		metrics.FailureRate = float64(failureEvents) / float64(totalEvents)
		metrics.DisputeRate = float64(disputeEvents) / float64(totalEvents)
	}

	return metrics
}

// performUserAnalysis 执行用户级分析
func (s *SchedulerService) performUserAnalysis(ctx context.Context, timeRange TimeRange, batchSize int) error {
	log.Println("Performing user-level analysis...")

	// 获取在指定时间范围内有活动的用户列表
	userIDs, err := s.getActiveUsers(timeRange)
	if err != nil {
		return fmt.Errorf("failed to get active users: %w", err)
	}

	log.Printf("Found %d active users for analysis", len(userIDs))

	// 批量处理用户分析
	for i := 0; i < len(userIDs); i += batchSize {
		end := i + batchSize
		if end > len(userIDs) {
			end = len(userIDs)
		}

		batch := userIDs[i:end]
		if err := s.analyzeUserBatch(ctx, batch, timeRange); err != nil {
			log.Printf("Failed to analyze user batch %d-%d: %v", i, end, err)
			continue
		}

		log.Printf("Completed analysis for users %d-%d", i, end)
	}

	return nil
}

// getActiveUsers 获取活跃用户列表
func (s *SchedulerService) getActiveUsers(timeRange TimeRange) ([]string, error) {
	var userIDs []string

	// 通过解析payload中的customer信息获取用户ID
	// 这是一个简化的实现，实际中可能需要更复杂的查询
	rows, err := s.db.Raw(`
		SELECT DISTINCT 
			JSON_UNQUOTE(JSON_EXTRACT(payload, '$.object.customer')) as user_id
		FROM stripe_events 
		WHERE created_at_stripe >= ? AND created_at_stripe <= ?
			AND payload LIKE '%"customer"%'
			AND JSON_UNQUOTE(JSON_EXTRACT(payload, '$.object.customer')) IS NOT NULL
			AND JSON_UNQUOTE(JSON_EXTRACT(payload, '$.object.customer')) != ''
	`, timeRange.StartTime, timeRange.EndTime).Rows()

	if err != nil {
		return nil, err
	}
	defer rows.Close()

	for rows.Next() {
		var userID string
		if err := rows.Scan(&userID); err != nil {
			continue
		}
		if userID != "" && userID != "null" {
			userIDs = append(userIDs, userID)
		}
	}

	return userIDs, nil
}

// analyzeUserBatch 分析用户批次
func (s *SchedulerService) analyzeUserBatch(ctx context.Context, userIDs []string, timeRange TimeRange) error {
	for _, userID := range userIDs {
		analysis, err := s.paymentAnalysisService.AnalyzeUserPaymentsByTimeRange(ctx, userID, timeRange)
		if err != nil {
			log.Printf("Failed to analyze user %s: %v", userID, err)
			continue
		}

		// 记录用户分析结果
		s.logUserAnalysis(analysis)

		// 这里可以将分析结果存储到数据库或发送到监控系统
	}

	return nil
}

// logUserAnalysis 记录用户分析结果
func (s *SchedulerService) logUserAnalysis(analysis *UserPaymentAnalysis) {
	if analysis.TotalPayments == 0 {
		return // 跳过没有支付活动的用户
	}

	log.Printf("User Analysis - UserID: %s", analysis.UserID)
	log.Printf("  Total Payments: %d", analysis.TotalPayments)
	log.Printf("  Success Rate: %.2f%%", (1-analysis.PaymentFailureRate)*100)
	log.Printf("  Failure Rate: %.2f%%", analysis.PaymentFailureRate*100)
	log.Printf("  Dispute Rate: %.2f%%", analysis.DisputeRate*100)
	log.Printf("  Charge Frequency: %.2f per day", analysis.ChargeFrequency)
	log.Printf("  Unique Cards Used: %d", analysis.UniqueCardsUsed)

	if len(analysis.FailureReasons) > 0 {
		log.Printf("  Failure Reasons:")
		for reason, count := range analysis.FailureReasons {
			log.Printf("    %s: %d", reason, count)
		}
	}
}

// processUnhandledEvents 处理未处理的事件
func (s *SchedulerService) processUnhandledEvents(ctx context.Context, config AnalysisConfig) {
	events, err := s.stripeEventDAO.GetUnprocessedEvents(config.BatchSize)
	if err != nil {
		log.Printf("Failed to get unprocessed events: %v", err)
		return
	}

	if len(events) == 0 {
		return
	}

	log.Printf("Processing %d unprocessed events", len(events))

	for _, event := range events {
		if err := s.processEvent(ctx, event); err != nil {
			log.Printf("Failed to process event %s: %v", event.EventID, err)
			s.stripeEventDAO.MarkEventAsError(event.ID, err.Error())
		} else {
			s.stripeEventDAO.MarkEventAsProcessed(event.ID)
		}
	}
}

// processEvent 处理单个事件
func (s *SchedulerService) processEvent(ctx context.Context, event models.StripeEvent) error {
	// 验证payload格式
	var payloadData map[string]interface{}
	if err := json.Unmarshal([]byte(event.Payload), &payloadData); err != nil {
		return fmt.Errorf("invalid payload format: %w", err)
	}

	// 这里可以添加更多的事件处理逻辑
	// 例如：数据验证、业务规则检查等

	return nil
}

// contains 检查字符串是否包含子字符串
func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr ||
		(len(s) > len(substr) &&
			(s[:len(substr)] == substr || s[len(s)-len(substr):] == substr ||
				(len(s) > 2*len(substr) && s[len(s)/2-len(substr)/2:len(s)/2+len(substr)/2+len(substr)%2] == substr))))
}
