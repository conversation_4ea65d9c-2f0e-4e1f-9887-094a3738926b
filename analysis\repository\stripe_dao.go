package repository

import (
	"fmt"
	"time"

	"gorm.io/gorm"
	"topnetwork.ai/topai/chat-webserver/analysis/repository/models"
)

// PaginationParams 分页参数
type PaginationParams struct {
	Page     int `json:"page" form:"page"`           // 页码，从1开始
	PageSize int `json:"page_size" form:"page_size"` // 每页大小
	Offset   int `json:"-"`                          // 偏移量，内部计算
}

// PaginationResult 分页结果
type PaginationResult struct {
	Total       int64 `json:"total"`        // 总记录数
	Page        int   `json:"page"`         // 当前页码
	PageSize    int   `json:"page_size"`    // 每页大小
	TotalPages  int   `json:"total_pages"`  // 总页数
	HasNext     bool  `json:"has_next"`     // 是否有下一页
	HasPrevious bool  `json:"has_previous"` // 是否有上一页
}

// ValidateAndSetDefaults 验证并设置默认值
func (p *PaginationParams) ValidateAndSetDefaults() {
	if p.Page <= 0 {
		p.Page = 1
	}
	if p.PageSize <= 0 {
		p.PageSize = 20 // 默认每页20条
	}
	if p.PageSize > 100 {
		p.PageSize = 100 // 最大每页100条
	}
	p.Offset = (p.Page - 1) * p.PageSize
}

// StripeEventFilters 查询过滤器
type StripeEventFilters struct {
	EventTypes      []string   `json:"event_types" form:"event_types"`             // 事件类型过滤
	StartTime       *time.Time `json:"start_time" form:"start_time"`               // 开始时间
	EndTime         *time.Time `json:"end_time" form:"end_time"`                   // 结束时间
	Processed       *bool      `json:"processed" form:"processed"`                 // 处理状态
	CustomerID      string     `json:"customer_id" form:"customer_id"`             // 客户ID
	EventIDContains string     `json:"event_id_contains" form:"event_id_contains"` // 事件ID包含
}

// buildFilterQuery 构建过滤查询
func (dao *StripeEventDAO) buildFilterQuery(filters StripeEventFilters) *gorm.DB {
	query := dao.db.Model(&models.StripeEvent{})

	// 事件类型过滤
	if len(filters.EventTypes) > 0 {
		query = query.Where("event_type IN ?", filters.EventTypes)
	}

	// 时间范围过滤
	if filters.StartTime != nil {
		query = query.Where("created_at_stripe >= ?", *filters.StartTime)
	}
	if filters.EndTime != nil {
		query = query.Where("created_at_stripe <= ?", *filters.EndTime)
	}

	// 处理状态过滤
	if filters.Processed != nil {
		query = query.Where("processed = ?", *filters.Processed)
	}

	// 客户ID过滤（通过payload搜索）
	if filters.CustomerID != "" {
		query = query.Where("payload LIKE ?", fmt.Sprintf("%%\"customer\":\"%s\"%%", filters.CustomerID))
	}

	// 事件ID包含过滤
	if filters.EventIDContains != "" {
		query = query.Where("event_id LIKE ?", fmt.Sprintf("%%%s%%", filters.EventIDContains))
	}

	return query
}

// CalculatePaginationResult 计算分页结果
func (p *PaginationParams) CalculatePaginationResult(total int64) PaginationResult {
	totalPages := int((total + int64(p.PageSize) - 1) / int64(p.PageSize))

	return PaginationResult{
		Total:       total,
		Page:        p.Page,
		PageSize:    p.PageSize,
		TotalPages:  totalPages,
		HasNext:     p.Page < totalPages,
		HasPrevious: p.Page > 1,
	}
}

// StripeEventDAO Stripe事件数据访问对象
type StripeEventDAO struct {
	db *gorm.DB
}

// NewStripeEventDAO 创建StripeEventDAO实例
func NewStripeEventDAO(db *gorm.DB) *StripeEventDAO {
	return &StripeEventDAO{db: db}
}

// GetStripeEventsByCreatedAtStripe 根据事件产生时间区间查询 StripeEvent
func (dao *StripeEventDAO) GetStripeEventsByCreatedAtStripe(start, end time.Time) ([]models.StripeEvent, error) {
	var events []models.StripeEvent
	err := dao.db.Where("created_at_stripe >= ? AND created_at_stripe <= ?", start, end).Find(&events).Error
	return events, err
}

// GetStripeEventsByTimeRange 根据时间范围查询StripeEvent
func (dao *StripeEventDAO) GetStripeEventsByTimeRange(start, end time.Time, eventTypes []string) ([]models.StripeEvent, error) {
	var events []models.StripeEvent
	query := dao.db.Where("created_at_stripe >= ? AND created_at_stripe <= ?", start, end)

	if len(eventTypes) > 0 {
		query = query.Where("event_type IN ?", eventTypes)
	}

	err := query.Order("created_at_stripe ASC").Find(&events).Error
	return events, err
}

// GetUnprocessedEvents 获取未处理的事件
func (dao *StripeEventDAO) GetUnprocessedEvents(limit int) ([]models.StripeEvent, error) {
	var events []models.StripeEvent
	err := dao.db.Where("processed = ?", false).
		Order("created_at_stripe ASC").
		Limit(limit).
		Find(&events).Error
	return events, err
}

// GetEventCountByType 获取指定时间范围内各事件类型的数量
func (dao *StripeEventDAO) GetEventCountByType(start, end time.Time) (map[string]int64, error) {
	var results []struct {
		EventType string
		Count     int64
	}

	err := dao.db.Model(&models.StripeEvent{}).
		Select("event_type, COUNT(*) as count").
		Where("created_at_stripe >= ? AND created_at_stripe <= ?", start, end).
		Group("event_type").
		Scan(&results).Error

	if err != nil {
		return nil, err
	}

	countMap := make(map[string]int64)
	for _, result := range results {
		countMap[result.EventType] = result.Count
	}

	return countMap, nil
}

// GetEventsByEventType 根据事件类型和时间范围获取事件
func (dao *StripeEventDAO) GetEventsByEventType(eventType string, start, end time.Time, limit int) ([]models.StripeEvent, error) {
	var events []models.StripeEvent
	query := dao.db.Where("event_type = ? AND created_at_stripe >= ? AND created_at_stripe <= ?",
		eventType, start, end).
		Order("created_at_stripe ASC")

	if limit > 0 {
		query = query.Limit(limit)
	}

	err := query.Find(&events).Error
	return events, err
}

// PaginatedStripeEventsResult 分页查询结果
type PaginatedStripeEventsResult struct {
	Events     []models.StripeEvent `json:"events"`
	Pagination PaginationResult     `json:"pagination"`
}

// GetStripeEventsPaginated 分页查询StripeEvent
func (dao *StripeEventDAO) GetStripeEventsPaginated(params PaginationParams, filters StripeEventFilters) (*PaginatedStripeEventsResult, error) {
	// 验证并设置默认分页参数
	params.ValidateAndSetDefaults()

	// 构建查询
	query := dao.buildFilterQuery(filters)

	// 获取总数
	var total int64
	if err := query.Model(&models.StripeEvent{}).Count(&total).Error; err != nil {
		return nil, fmt.Errorf("failed to count events: %w", err)
	}

	// 获取分页数据
	var events []models.StripeEvent
	err := query.Offset(params.Offset).
		Limit(params.PageSize).
		Order("created_at_stripe DESC").
		Find(&events).Error

	if err != nil {
		return nil, fmt.Errorf("failed to fetch paginated events: %w", err)
	}

	// 计算分页信息
	pagination := params.CalculatePaginationResult(total)

	return &PaginatedStripeEventsResult{
		Events:     events,
		Pagination: pagination,
	}, nil
}

// MarkEventAsProcessed 标记事件为已处理
func (dao *StripeEventDAO) MarkEventAsProcessed(eventID int64) error {
	return dao.db.Model(&models.StripeEvent{}).
		Where("id = ?", eventID).
		Updates(map[string]interface{}{
			"processed":    true,
			"processed_at": time.Now(),
		}).Error
}

// MarkEventAsError 标记事件处理错误
func (dao *StripeEventDAO) MarkEventAsError(eventID int64, errorMsg string) error {
	return dao.db.Model(&models.StripeEvent{}).
		Where("id = ?", eventID).
		Updates(map[string]interface{}{
			"processing_error": errorMsg,
			"updated_at":       time.Now(),
		}).Error
}

// GetEventsByUserAndTimeRange 根据用户ID和时间范围获取事件
func (dao *StripeEventDAO) GetEventsByUserAndTimeRange(userID string, start, end time.Time) ([]models.StripeEvent, error) {
	var events []models.StripeEvent

	// 通过payload中的customer信息查找用户相关事件
	err := dao.db.Where("created_at_stripe >= ? AND created_at_stripe <= ? AND payload LIKE ?",
		start, end, fmt.Sprintf("%%\"customer\":\"%s\"%%", userID)).
		Order("created_at_stripe ASC").
		Find(&events).Error

	return events, err
}

// BatchUpdateProcessedStatus 批量更新处理状态
func (dao *StripeEventDAO) BatchUpdateProcessedStatus(eventIDs []int64, processed bool) error {
	updates := map[string]interface{}{
		"processed":    processed,
		"processed_at": time.Now(),
	}

	return dao.db.Model(&models.StripeEvent{}).
		Where("id IN ?", eventIDs).
		Updates(updates).Error
}

// GetUserEventsPaginated 分页查询用户相关事件
func (dao *StripeEventDAO) GetUserEventsPaginated(userID string, params PaginationParams, filters StripeEventFilters) (*PaginatedStripeEventsResult, error) {
	// 设置用户ID过滤
	filters.CustomerID = userID

	return dao.GetStripeEventsPaginated(params, filters)
}

// GetEventsByTypePaginated 按事件类型分页查询
func (dao *StripeEventDAO) GetEventsByTypePaginated(eventType string, params PaginationParams, filters StripeEventFilters) (*PaginatedStripeEventsResult, error) {
	// 设置事件类型过滤
	filters.EventTypes = []string{eventType}

	return dao.GetStripeEventsPaginated(params, filters)
}

// GetUnprocessedEventsPaginated 分页查询未处理事件
func (dao *StripeEventDAO) GetUnprocessedEventsPaginated(params PaginationParams) (*PaginatedStripeEventsResult, error) {
	processed := false
	filters := StripeEventFilters{
		Processed: &processed,
	}

	return dao.GetStripeEventsPaginated(params, filters)
}
