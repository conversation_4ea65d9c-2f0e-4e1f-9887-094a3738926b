package repository

import (
	"fmt"
	"time"

	"gorm.io/gorm"
	"topnetwork.ai/topai/chat-webserver/analysis/repository/models"
)

// StripeEventDAO Stripe事件数据访问对象
type StripeEventDAO struct {
	db *gorm.DB
}

// NewStripeEventDAO 创建StripeEventDAO实例
func NewStripeEventDAO(db *gorm.DB) *StripeEventDAO {
	return &StripeEventDAO{db: db}
}

// GetStripeEventsByCreatedAtStripe 根据事件产生时间区间查询 StripeEvent
func (dao *StripeEventDAO) GetStripeEventsByCreatedAtStripe(start, end time.Time) ([]models.StripeEvent, error) {
	var events []models.StripeEvent
	err := dao.db.Where("created_at_stripe >= ? AND created_at_stripe <= ?", start, end).Find(&events).Error
	return events, err
}

// GetStripeEventsByTimeRange 根据时间范围查询StripeEvent
func (dao *StripeEventDAO) GetStripeEventsByTimeRange(start, end time.Time, eventTypes []string) ([]models.StripeEvent, error) {
	var events []models.StripeEvent
	query := dao.db.Where("created_at_stripe >= ? AND created_at_stripe <= ?", start, end)

	if len(eventTypes) > 0 {
		query = query.Where("event_type IN ?", eventTypes)
	}

	err := query.Order("created_at_stripe ASC").Find(&events).Error
	return events, err
}

// GetUnprocessedEvents 获取未处理的事件
func (dao *StripeEventDAO) GetUnprocessedEvents(limit int) ([]models.StripeEvent, error) {
	var events []models.StripeEvent
	err := dao.db.Where("processed = ?", false).
		Order("created_at_stripe ASC").
		Limit(limit).
		Find(&events).Error
	return events, err
}

// MarkEventAsProcessed 标记事件为已处理
func (dao *StripeEventDAO) MarkEventAsProcessed(eventID int64) error {
	return dao.db.Model(&models.StripeEvent{}).
		Where("id = ?", eventID).
		Updates(map[string]interface{}{
			"processed":    true,
			"processed_at": time.Now(),
		}).Error
}

// MarkEventAsError 标记事件处理错误
func (dao *StripeEventDAO) MarkEventAsError(eventID int64, errorMsg string) error {
	return dao.db.Model(&models.StripeEvent{}).
		Where("id = ?", eventID).
		Updates(map[string]interface{}{
			"processing_error": errorMsg,
			"updated_at":       time.Now(),
		}).Error
}

// GetEventsByUserAndTimeRange 根据用户ID和时间范围获取事件
func (dao *StripeEventDAO) GetEventsByUserAndTimeRange(userID string, start, end time.Time) ([]models.StripeEvent, error) {
	var events []models.StripeEvent

	// 通过payload中的customer信息查找用户相关事件
	err := dao.db.Where("created_at_stripe >= ? AND created_at_stripe <= ? AND payload LIKE ?",
		start, end, fmt.Sprintf("%%\"customer\":\"%s\"%%", userID)).
		Order("created_at_stripe ASC").
		Find(&events).Error

	return events, err
}

// GetEventCountByType 获取指定时间范围内各事件类型的数量
func (dao *StripeEventDAO) GetEventCountByType(start, end time.Time) (map[string]int64, error) {
	var results []struct {
		EventType string
		Count     int64
	}

	err := dao.db.Model(&models.StripeEvent{}).
		Select("event_type, COUNT(*) as count").
		Where("created_at_stripe >= ? AND created_at_stripe <= ?", start, end).
		Group("event_type").
		Scan(&results).Error

	if err != nil {
		return nil, err
	}

	countMap := make(map[string]int64)
	for _, result := range results {
		countMap[result.EventType] = result.Count
	}

	return countMap, nil
}

// GetEventsByEventType 根据事件类型和时间范围获取事件
func (dao *StripeEventDAO) GetEventsByEventType(eventType string, start, end time.Time, limit int) ([]models.StripeEvent, error) {
	var events []models.StripeEvent
	query := dao.db.Where("event_type = ? AND created_at_stripe >= ? AND created_at_stripe <= ?",
		eventType, start, end).
		Order("created_at_stripe ASC")

	if limit > 0 {
		query = query.Limit(limit)
	}

	err := query.Find(&events).Error
	return events, err
}

// BatchUpdateProcessedStatus 批量更新处理状态
func (dao *StripeEventDAO) BatchUpdateProcessedStatus(eventIDs []int64, processed bool) error {
	updates := map[string]interface{}{
		"processed":    processed,
		"processed_at": time.Now(),
	}

	return dao.db.Model(&models.StripeEvent{}).
		Where("id IN ?", eventIDs).
		Updates(updates).Error
}
