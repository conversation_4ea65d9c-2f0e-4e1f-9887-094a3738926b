


// GetStripeEventsByCreatedAtStripe 根据事件产生时间区间查询 StripeEvent
func GetStripeEventsByCreatedAtStripe(db *gorm.DB, start, end time.Time) ([]StripeEvent, error) {
	var events []StripeEvent
	err := db.Where("created_at_stripe >= ? AND created_at_stripe <= ?", start, end).Find(&events).Error
	return events, err
}

// ProcessStripeEvent 根据 EventType 和 Payload 处理 StripeEvent
func ProcessStripeEvent(event StripeEvent) error {
	// 解析 payload
	var payloadData map[string]interface{}
	if err := json.Unmarshal([]byte(event.Payload), &payloadData); err != nil {
		return fmt.Errorf("payload unmarshal error: %w", err)
	}

	// 根据 EventType 处理
	switch event.EventType {
	case "payment_intent.succeeded":
		// 处理支付成功事件
		// 例如：payloadData["object"] 可获取具体数据
		// ...业务逻辑...
	case "payment_intent.payment_failed":
		// 处理支付失败事件
		// ...业务逻辑...
	case "charge.refunded":
		// 处理退款事件
		// ...业务逻辑...
	// 可根据需要扩展更多事件类型
	default:
		// 未知事件类型处理
		// ...业务逻辑...
	}
	return nil
}