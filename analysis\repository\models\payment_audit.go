package models

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"
)

// StripeEvent Stripe事件存储模型
type StripeEvent struct {
	ID              int64
	EventID         string
	EventType       string
	SessionStatus   int
	Payload         string
	Processed       bool
	ProcessedAt     time.Time
	CreatedAt       time.Time
	UpdatedAt       time.Time
	ProcessingError string
	CreatedAtStripe time.Time
}

// PaymentEventType 支付事件类型枚举
type PaymentEventType string

const (
	PaymentSucceeded      PaymentEventType = "payment_succeeded"
	PaymentFailed         PaymentEventType = "payment_failed"
	DisputeCreated        PaymentEventType = "dispute_created"
	RefundCreated         PaymentEventType = "refund_created"
	PaymentMethodAttached PaymentEventType = "payment_method_attached"
)

// PaymentEvent 支付事件详细记录模型
type PaymentEvent struct {
	ID                    int64            `gorm:"primaryKey;autoIncrement" json:"id"`
	StripeEventID         int64            `gorm:"index;not null" json:"stripe_event_id"`
	UserID                string           `gorm:"index;size:255;not null" json:"user_id"`
	StripeCustomerID      string           `gorm:"index;size:255;not null" json:"stripe_customer_id"`
	StripeChargeID        *string          `gorm:"size:255" json:"stripe_charge_id"`
	StripePaymentIntentID *string          `gorm:"size:255" json:"stripe_payment_intent_id"`
	EventType             PaymentEventType `gorm:"index;not null" json:"event_type"`
	Amount                *float64         `gorm:"type:decimal(15,2)" json:"amount"`
	Currency              *string          `gorm:"size:10" json:"currency"`
	Status                *string          `gorm:"size:50" json:"status"`
	FailureCode           *string          `gorm:"index;size:100" json:"failure_code"`
	FailureMessage        *string          `gorm:"type:text" json:"failure_message"`
	OutcomeType           *string          `gorm:"index;size:50" json:"outcome_type"`
	OutcomeNetworkStatus  *string          `gorm:"size:50" json:"outcome_network_status"`
	OutcomeReason         *string          `gorm:"size:100" json:"outcome_reason"`
	OutcomeSellerMessage  *string          `gorm:"type:text" json:"outcome_seller_message"`
	CardCountry           *string          `gorm:"index;size:10" json:"card_country"`
	CardBrand             *string          `gorm:"size:50" json:"card_brand"`
	CardFunding           *string          `gorm:"size:50" json:"card_funding"`
	CardLast4             *string          `gorm:"size:10" json:"card_last4"`
	DisputeReason         *string          `gorm:"size:100" json:"dispute_reason"`
	DisputeStatus         *string          `gorm:"size:50" json:"dispute_status"`
	RefundReason          *string          `gorm:"size:100" json:"refund_reason"`
	ClientIP              *string          `gorm:"size:45" json:"client_ip"`
	UserAgent             *string          `gorm:"type:text" json:"user_agent"`
	CreatedAt             time.Time        `gorm:"index;autoCreateTime" json:"created_at"`

	// 关联关系
	StripeEvent StripeEvent `gorm:"foreignKey:StripeEventID" json:"stripe_event,omitempty"`
}

// UserPaymentDailyStats 用户支付统计模型（按日汇总）
type UserPaymentDailyStats struct {
	ID                 int64     `gorm:"primaryKey;autoIncrement" json:"id"`
	UserID             string    `gorm:"index;size:255;not null" json:"user_id"`
	StatDate           time.Time `gorm:"index;type:date;not null" json:"stat_date"`
	TotalPayments      int       `gorm:"default:0" json:"total_payments"`
	SuccessfulPayments int       `gorm:"default:0" json:"successful_payments"`
	FailedPayments     int       `gorm:"default:0" json:"failed_payments"`
	DisputedPayments   int       `gorm:"default:0" json:"disputed_payments"`
	RefundedPayments   int       `gorm:"default:0" json:"refunded_payments"`
	TotalAmount        float64   `gorm:"type:decimal(15,2);default:0.00" json:"total_amount"`
	SuccessfulAmount   float64   `gorm:"type:decimal(15,2);default:0.00" json:"successful_amount"`
	UniqueCardsUsed    int       `gorm:"default:0" json:"unique_cards_used"`
	UniqueCountries    int       `gorm:"default:0" json:"unique_countries"`
	CreatedAt          time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt          time.Time `gorm:"autoUpdateTime" json:"updated_at"`
}

// FailureType 失败类型枚举
type FailureType string

const (
	FailureCodeType    FailureType = "failure_code"
	OutcomeTypeFailure FailureType = "outcome_type"
	DisputeReasonType  FailureType = "dispute_reason"
)

// PaymentFailureStats 支付失败原因统计模型
type PaymentFailureStats struct {
	ID             int64       `gorm:"primaryKey;autoIncrement" json:"id"`
	UserID         string      `gorm:"index;size:255;not null" json:"user_id"`
	FailureType    FailureType `gorm:"index;not null" json:"failure_type"`
	FailureValue   string      `gorm:"index;size:100;not null" json:"failure_value"`
	Count          int         `gorm:"default:1" json:"count"`
	LastOccurredAt time.Time   `gorm:"index;not null" json:"last_occurred_at"`
	CreatedAt      time.Time   `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt      time.Time   `gorm:"autoUpdateTime" json:"updated_at"`
}

// UserCardUsage 用户银行卡使用记录模型
type UserCardUsage struct {
	ID              int64     `gorm:"primaryKey;autoIncrement" json:"id"`
	UserID          string    `gorm:"index;size:255;not null" json:"user_id"`
	CardFingerprint string    `gorm:"size:255;not null" json:"card_fingerprint"`
	CardBrand       *string   `gorm:"index;size:50" json:"card_brand"`
	CardCountry     *string   `gorm:"index;size:10" json:"card_country"`
	CardFunding     *string   `gorm:"size:50" json:"card_funding"`
	CardLast4       *string   `gorm:"size:10" json:"card_last4"`
	FirstUsedAt     time.Time `gorm:"index;not null" json:"first_used_at"`
	LastUsedAt      time.Time `gorm:"index;not null" json:"last_used_at"`
	UsageCount      int       `gorm:"default:1" json:"usage_count"`
	SuccessfulCount int       `gorm:"default:0" json:"successful_count"`
	FailedCount     int       `gorm:"default:0" json:"failed_count"`
	CreatedAt       time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt       time.Time `gorm:"autoUpdateTime" json:"updated_at"`
}

// RiskLevel 风险等级枚举
type RiskLevel string

const (
	RiskLevelLow     RiskLevel = "low"
	RiskLevelMedium  RiskLevel = "medium"
	RiskLevelHigh    RiskLevel = "high"
	RiskLevelBlocked RiskLevel = "blocked"
)

// GeoRiskConfig 地理风险配置模型
type GeoRiskConfig struct {
	ID          int64     `gorm:"primaryKey;autoIncrement" json:"id"`
	CountryCode string    `gorm:"uniqueIndex;size:10;not null" json:"country_code"`
	RiskLevel   RiskLevel `gorm:"default:low" json:"risk_level"`
	IsAllowed   bool      `gorm:"default:true" json:"is_allowed"`
	Description *string   `gorm:"type:text" json:"description"`
	CreatedAt   time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt   time.Time `gorm:"autoUpdateTime" json:"updated_at"`
}

// JSON 自定义JSON类型，用于处理GORM的JSON字段
type JSON map[string]interface{}

// Value 实现driver.Valuer接口
func (j JSON) Value() (driver.Value, error) {
	if j == nil {
		return nil, nil
	}
	return json.Marshal(j)
}

// Scan 实现sql.Scanner接口
func (j *JSON) Scan(value interface{}) error {
	if value == nil {
		*j = nil
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("cannot scan %T into JSON", value)
	}

	return json.Unmarshal(bytes, j)
}

// TableName 设置表名
func (StripeEvent) TableName() string {
	return "stripe_events"
}

func (PaymentEvent) TableName() string {
	return "payment_events"
}

func (UserPaymentDailyStats) TableName() string {
	return "user_payment_daily_stats"
}

func (PaymentFailureStats) TableName() string {
	return "payment_failure_stats"
}

func (UserCardUsage) TableName() string {
	return "user_card_usage"
}

func (GeoRiskConfig) TableName() string {
	return "geo_risk_config"
}
