package main

import (
	"encoding/json"
	"fmt"
	"log"
	"time"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"topnetwork.ai/topai/chat-webserver/analysis/repository"
	"topnetwork.ai/topai/chat-webserver/analysis/repository/models"
)

// 这个文件演示了如何使用分页查询功能

func main() {
	// 初始化数据库连接
	dsn := "root:password@tcp(localhost:3306)/payment_analysis?charset=utf8mb4&parseTime=True&loc=Local"
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// 自动迁移表结构
	err = db.AutoMigrate(&models.StripeEvent{})
	if err != nil {
		log.Fatalf("Failed to migrate database: %v", err)
	}

	// 创建DAO实例
	stripeEventDAO := repository.NewStripeEventDAO(db)

	// 插入测试数据
	insertTestData(db)

	// 演示分页查询功能
	demonstratePagination(stripeEventDAO)

	log.Println("分页查询演示完成!")
}

// insertTestData 插入测试数据
func insertTestData(db *gorm.DB) {
	log.Println("插入测试数据...")

	// 创建多个测试事件
	events := []models.StripeEvent{}
	
	eventTypes := []string{
		"payment_intent.succeeded",
		"payment_intent.payment_failed", 
		"charge.dispute.created",
		"charge.succeeded",
		"charge.failed",
	}
	
	customers := []string{"cus_test001", "cus_test002", "cus_test003"}

	// 生成50个测试事件
	for i := 0; i < 50; i++ {
		eventType := eventTypes[i%len(eventTypes)]
		customer := customers[i%len(customers)]
		
		payload := createTestPayload(eventType, customer, (i+1)*100, "usd")
		
		event := models.StripeEvent{
			EventID:         fmt.Sprintf("evt_test_%03d", i+1),
			EventType:       eventType,
			Payload:         payload,
			CreatedAtStripe: time.Now().Add(-time.Duration(i) * time.Hour),
			Processed:       i%3 == 0, // 每3个事件中有1个已处理
		}
		
		events = append(events, event)
	}

	// 批量插入
	result := db.Create(&events)
	if result.Error != nil {
		log.Printf("Failed to insert test data: %v", result.Error)
		return
	}

	log.Printf("成功插入 %d 条测试数据", len(events))
}

// createTestPayload 创建测试负载
func createTestPayload(eventType, customerID string, amount int, currency string) string {
	payload := map[string]interface{}{
		"object": map[string]interface{}{
			"id":       fmt.Sprintf("pi_%s_%d", eventType, amount),
			"customer": customerID,
			"amount":   amount,
			"currency": currency,
			"status":   "succeeded",
		},
	}

	if eventType == "payment_intent.payment_failed" || eventType == "charge.failed" {
		payload["object"].(map[string]interface{})["status"] = "failed"
		payload["object"].(map[string]interface{})["last_payment_error"] = map[string]interface{}{
			"code":    "card_declined",
			"message": "Your card was declined.",
		}
	}

	jsonData, _ := json.Marshal(payload)
	return string(jsonData)
}

// demonstratePagination 演示分页查询功能
func demonstratePagination(dao *repository.StripeEventDAO) {
	log.Println("\n=== 分页查询演示 ===")

	// 1. 基本分页查询
	log.Println("\n1. 基本分页查询 (第1页，每页10条)")
	params := repository.PaginationParams{
		Page:     1,
		PageSize: 10,
	}
	filters := repository.StripeEventFilters{}
	
	result, err := dao.GetStripeEventsPaginated(params, filters)
	if err != nil {
		log.Printf("分页查询失败: %v", err)
		return
	}
	
	printPaginationResult("基本分页查询", result)

	// 2. 按事件类型过滤
	log.Println("\n2. 按事件类型过滤 (只查询成功的支付)")
	filters.EventTypes = []string{"payment_intent.succeeded", "charge.succeeded"}
	
	result, err = dao.GetStripeEventsPaginated(params, filters)
	if err != nil {
		log.Printf("事件类型过滤查询失败: %v", err)
		return
	}
	
	printPaginationResult("事件类型过滤", result)

	// 3. 按时间范围过滤
	log.Println("\n3. 按时间范围过滤 (最近24小时)")
	now := time.Now()
	yesterday := now.Add(-24 * time.Hour)
	filters = repository.StripeEventFilters{
		StartTime: &yesterday,
		EndTime:   &now,
	}
	
	result, err = dao.GetStripeEventsPaginated(params, filters)
	if err != nil {
		log.Printf("时间范围过滤查询失败: %v", err)
		return
	}
	
	printPaginationResult("时间范围过滤", result)

	// 4. 按处理状态过滤
	log.Println("\n4. 按处理状态过滤 (只查询未处理的事件)")
	processed := false
	filters = repository.StripeEventFilters{
		Processed: &processed,
	}
	
	result, err = dao.GetStripeEventsPaginated(params, filters)
	if err != nil {
		log.Printf("处理状态过滤查询失败: %v", err)
		return
	}
	
	printPaginationResult("未处理事件", result)

	// 5. 按客户ID过滤
	log.Println("\n5. 按客户ID过滤 (查询特定用户)")
	filters = repository.StripeEventFilters{
		CustomerID: "cus_test001",
	}
	
	result, err = dao.GetStripeEventsPaginated(params, filters)
	if err != nil {
		log.Printf("客户ID过滤查询失败: %v", err)
		return
	}
	
	printPaginationResult("特定用户事件", result)

	// 6. 组合过滤条件
	log.Println("\n6. 组合过滤条件 (特定用户的失败支付)")
	filters = repository.StripeEventFilters{
		CustomerID: "cus_test001",
		EventTypes: []string{"payment_intent.payment_failed", "charge.failed"},
	}
	
	result, err = dao.GetStripeEventsPaginated(params, filters)
	if err != nil {
		log.Printf("组合过滤查询失败: %v", err)
		return
	}
	
	printPaginationResult("组合过滤", result)

	// 7. 演示分页导航
	log.Println("\n7. 分页导航演示")
	demonstratePaginationNavigation(dao)

	// 8. 使用便捷方法
	log.Println("\n8. 便捷方法演示")
	demonstrateConvenienceMethods(dao)
}

// printPaginationResult 打印分页结果
func printPaginationResult(title string, result *repository.PaginatedStripeEventsResult) {
	log.Printf("=== %s ===", title)
	log.Printf("总记录数: %d", result.Pagination.Total)
	log.Printf("当前页: %d/%d", result.Pagination.Page, result.Pagination.TotalPages)
	log.Printf("每页大小: %d", result.Pagination.PageSize)
	log.Printf("有下一页: %t", result.Pagination.HasNext)
	log.Printf("有上一页: %t", result.Pagination.HasPrevious)
	log.Printf("本页事件数: %d", len(result.Events))
	
	// 显示前3个事件的基本信息
	for i, event := range result.Events {
		if i >= 3 {
			log.Printf("... 还有 %d 个事件", len(result.Events)-3)
			break
		}
		log.Printf("  事件 %d: %s (%s) - 处理状态: %t", 
			i+1, event.EventID, event.EventType, event.Processed)
	}
}

// demonstratePaginationNavigation 演示分页导航
func demonstratePaginationNavigation(dao *repository.StripeEventDAO) {
	params := repository.PaginationParams{
		Page:     1,
		PageSize: 5, // 小页面便于演示
	}
	filters := repository.StripeEventFilters{}

	// 遍历前3页
	for page := 1; page <= 3; page++ {
		params.Page = page
		result, err := dao.GetStripeEventsPaginated(params, filters)
		if err != nil {
			log.Printf("第%d页查询失败: %v", page, err)
			continue
		}

		log.Printf("第 %d 页: %d 个事件", page, len(result.Events))
		
		// 如果没有更多页面，停止
		if !result.Pagination.HasNext {
			log.Printf("已到达最后一页")
			break
		}
	}
}

// demonstrateConvenienceMethods 演示便捷方法
func demonstrateConvenienceMethods(dao *repository.StripeEventDAO) {
	params := repository.PaginationParams{
		Page:     1,
		PageSize: 5,
	}

	// 1. 查询用户事件
	log.Println("查询用户 cus_test002 的事件:")
	result, err := dao.GetUserEventsPaginated("cus_test002", params, repository.StripeEventFilters{})
	if err != nil {
		log.Printf("查询用户事件失败: %v", err)
	} else {
		log.Printf("找到 %d 个用户事件", len(result.Events))
	}

	// 2. 查询特定类型事件
	log.Println("查询支付成功事件:")
	result, err = dao.GetEventsByTypePaginated("payment_intent.succeeded", params, repository.StripeEventFilters{})
	if err != nil {
		log.Printf("查询特定类型事件失败: %v", err)
	} else {
		log.Printf("找到 %d 个支付成功事件", len(result.Events))
	}

	// 3. 查询未处理事件
	log.Println("查询未处理事件:")
	result, err = dao.GetUnprocessedEventsPaginated(params)
	if err != nil {
		log.Printf("查询未处理事件失败: %v", err)
	} else {
		log.Printf("找到 %d 个未处理事件", len(result.Events))
	}
}
