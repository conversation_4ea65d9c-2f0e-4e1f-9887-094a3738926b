package service

import (
	"context"
	"fmt"
	"strings"
	"time"

	"topnetwork.ai/topai/chat-webserver/analysis/repository"
	"topnetwork.ai/topai/chat-webserver/analysis/repository/models"
)

// FailureAnalysisService 失败原因分析服务
type FailureAnalysisService struct {
	db *repository.DB
}

// NewFailureAnalysisService 创建失败原因分析服务实例
func NewFailureAnalysisService(db *repository.DB) *FailureAnalysisService {
	return &FailureAnalysisService{
		db: db,
	}
}

// FailureCategory 失败分类
type FailureCategory struct {
	Category    string `json:"category"`
	SubCategory string `json:"sub_category"`
	Description string `json:"description"`
	Severity    string `json:"severity"`
	Actionable  bool   `json:"actionable"`
}

// FailureAnalysisResult 失败分析结果
type FailureAnalysisResult struct {
	UserID              string                  `json:"user_id"`
	TimeRange           TimeRange               `json:"time_range"`
	TotalFailures       int                     `json:"total_failures"`
	FailuresByCode      map[string]int          `json:"failures_by_code"`
	FailuresByOutcome   map[string]int          `json:"failures_by_outcome"`
	FailuresByDispute   map[string]int          `json:"failures_by_dispute"`
	CategorizedFailures map[string]FailureStats `json:"categorized_failures"`
	Recommendations     []string                `json:"recommendations"`
}

// FailureStats 失败统计
type FailureStats struct {
	Count      int             `json:"count"`
	Percentage float64         `json:"percentage"`
	Category   FailureCategory `json:"category"`
	FirstSeen  time.Time       `json:"first_seen"`
	LastSeen   time.Time       `json:"last_seen"`
}

// Stripe错误代码分类映射
var stripeErrorCategories = map[string]FailureCategory{
	// 卡片相关错误
	"card_declined": {
		Category:    "card_issues",
		SubCategory: "declined",
		Description: "Card was declined by the issuing bank",
		Severity:    "medium",
		Actionable:  true,
	},
	"insufficient_funds": {
		Category:    "card_issues",
		SubCategory: "insufficient_funds",
		Description: "Insufficient funds in the account",
		Severity:    "medium",
		Actionable:  true,
	},
	"expired_card": {
		Category:    "card_issues",
		SubCategory: "expired",
		Description: "Card has expired",
		Severity:    "high",
		Actionable:  true,
	},
	"incorrect_cvc": {
		Category:    "card_issues",
		SubCategory: "security_code",
		Description: "Incorrect security code",
		Severity:    "medium",
		Actionable:  true,
	},
	"processing_error": {
		Category:    "system_issues",
		SubCategory: "processing",
		Description: "Error occurred while processing the payment",
		Severity:    "low",
		Actionable:  false,
	},
	"lost_card": {
		Category:    "fraud_risk",
		SubCategory: "lost_stolen",
		Description: "Card reported as lost",
		Severity:    "high",
		Actionable:  false,
	},
	"stolen_card": {
		Category:    "fraud_risk",
		SubCategory: "lost_stolen",
		Description: "Card reported as stolen",
		Severity:    "high",
		Actionable:  false,
	},
	"fraudulent": {
		Category:    "fraud_risk",
		SubCategory: "fraudulent",
		Description: "Transaction flagged as fraudulent",
		Severity:    "critical",
		Actionable:  false,
	},
}

// Outcome类型分类映射
var outcomeCategories = map[string]FailureCategory{
	"issuer_declined": {
		Category:    "issuer_issues",
		SubCategory: "declined",
		Description: "Payment declined by card issuer",
		Severity:    "medium",
		Actionable:  true,
	},
	"blocked": {
		Category:    "fraud_prevention",
		SubCategory: "blocked",
		Description: "Payment blocked by fraud prevention",
		Severity:    "high",
		Actionable:  false,
	},
	"invalid": {
		Category:    "validation_errors",
		SubCategory: "invalid_data",
		Description: "Invalid payment data",
		Severity:    "medium",
		Actionable:  true,
	},
	"manual_review": {
		Category:    "risk_management",
		SubCategory: "manual_review",
		Description: "Payment requires manual review",
		Severity:    "low",
		Actionable:  false,
	},
}

// 争议原因分类映射
var disputeCategories = map[string]FailureCategory{
	"fraudulent": {
		Category:    "chargeback",
		SubCategory: "fraud",
		Description: "Customer claims transaction is fraudulent",
		Severity:    "critical",
		Actionable:  false,
	},
	"unrecognized": {
		Category:    "chargeback",
		SubCategory: "unrecognized",
		Description: "Customer doesn't recognize the transaction",
		Severity:    "high",
		Actionable:  true,
	},
	"duplicate": {
		Category:    "chargeback",
		SubCategory: "duplicate",
		Description: "Customer claims duplicate charge",
		Severity:    "medium",
		Actionable:  true,
	},
	"subscription_canceled": {
		Category:    "chargeback",
		SubCategory: "service_issue",
		Description: "Customer canceled subscription but was still charged",
		Severity:    "medium",
		Actionable:  true,
	},
	"product_not_received": {
		Category:    "chargeback",
		SubCategory: "service_issue",
		Description: "Customer claims product/service not received",
		Severity:    "medium",
		Actionable:  true,
	},
	"product_unacceptable": {
		Category:    "chargeback",
		SubCategory: "service_issue",
		Description: "Customer claims product/service unacceptable",
		Severity:    "medium",
		Actionable:  true,
	},
}

// AnalyzeFailures 分析用户失败原因
func (s *FailureAnalysisService) AnalyzeFailures(ctx context.Context, userID string, timeRange TimeRange) (*FailureAnalysisResult, error) {
	result := &FailureAnalysisResult{
		UserID:              userID,
		TimeRange:           timeRange,
		FailuresByCode:      make(map[string]int),
		FailuresByOutcome:   make(map[string]int),
		FailuresByDispute:   make(map[string]int),
		CategorizedFailures: make(map[string]FailureStats),
		Recommendations:     []string{},
	}

	// 获取失败统计数据
	var failureStats []models.PaymentFailureStats
	err := s.db.GetDB().Where("user_id = ? AND last_occurred_at BETWEEN ? AND ?",
		userID, timeRange.StartTime, timeRange.EndTime).Find(&failureStats).Error
	if err != nil {
		return nil, fmt.Errorf("failed to fetch failure stats: %w", err)
	}

	// 分析失败原因
	for _, stat := range failureStats {
		result.TotalFailures += stat.Count

		switch stat.FailureType {
		case models.FailureCodeType:
			result.FailuresByCode[stat.FailureValue] = stat.Count
			s.categorizeFailure(result, stat.FailureValue, stat.Count, stat.LastOccurredAt, stripeErrorCategories)
		case models.OutcomeTypeFailure:
			if strings.HasPrefix(stat.FailureValue, "outcome_") {
				outcomeType := strings.TrimPrefix(stat.FailureValue, "outcome_")
				result.FailuresByOutcome[outcomeType] = stat.Count
				s.categorizeFailure(result, outcomeType, stat.Count, stat.LastOccurredAt, outcomeCategories)
			}
		case models.DisputeReasonType:
			if strings.HasPrefix(stat.FailureValue, "dispute_") {
				disputeReason := strings.TrimPrefix(stat.FailureValue, "dispute_")
				result.FailuresByDispute[disputeReason] = stat.Count
				s.categorizeFailure(result, disputeReason, stat.Count, stat.LastOccurredAt, disputeCategories)
			}
		}
	}

	// 计算百分比
	s.calculatePercentages(result)

	// 生成建议
	result.Recommendations = s.generateRecommendations(result)

	return result, nil
}

// categorizeFailure 对失败进行分类
func (s *FailureAnalysisService) categorizeFailure(result *FailureAnalysisResult, failureValue string, count int, lastOccurred time.Time, categories map[string]FailureCategory) {
	category, exists := categories[failureValue]
	if !exists {
		// 未知错误类型，创建默认分类
		category = FailureCategory{
			Category:    "unknown",
			SubCategory: "unclassified",
			Description: fmt.Sprintf("Unknown failure type: %s", failureValue),
			Severity:    "low",
			Actionable:  false,
		}
	}

	categoryKey := fmt.Sprintf("%s_%s", category.Category, category.SubCategory)

	if existing, exists := result.CategorizedFailures[categoryKey]; exists {
		existing.Count += count
		if lastOccurred.After(existing.LastSeen) {
			existing.LastSeen = lastOccurred
		}
		result.CategorizedFailures[categoryKey] = existing
	} else {
		result.CategorizedFailures[categoryKey] = FailureStats{
			Count:     count,
			Category:  category,
			FirstSeen: lastOccurred,
			LastSeen:  lastOccurred,
		}
	}
}

// calculatePercentages 计算百分比
func (s *FailureAnalysisService) calculatePercentages(result *FailureAnalysisResult) {
	if result.TotalFailures == 0 {
		return
	}

	for key, stats := range result.CategorizedFailures {
		stats.Percentage = float64(stats.Count) / float64(result.TotalFailures) * 100
		result.CategorizedFailures[key] = stats
	}
}

// generateRecommendations 生成建议
func (s *FailureAnalysisService) generateRecommendations(result *FailureAnalysisResult) []string {
	var recommendations []string

	// 基于失败分类生成建议
	for _, stats := range result.CategorizedFailures {
		if stats.Percentage > 20 { // 超过20%的失败率
			switch stats.Category.Category {
			case "card_issues":
				recommendations = append(recommendations, "建议提醒用户检查银行卡信息，包括有效期和余额")
			case "fraud_risk":
				recommendations = append(recommendations, "检测到高风险交易，建议加强身份验证")
			case "issuer_issues":
				recommendations = append(recommendations, "发卡行拒绝较多，建议用户联系银行或尝试其他支付方式")
			case "chargeback":
				recommendations = append(recommendations, "争议率较高，建议改善客户服务和交易描述")
			}
		}
	}

	// 基于失败频率生成建议
	if result.TotalFailures > 10 {
		recommendations = append(recommendations, "失败次数较多，建议优化支付流程")
	}

	// 基于特定错误代码生成建议
	if count, exists := result.FailuresByCode["insufficient_funds"]; exists && count > 3 {
		recommendations = append(recommendations, "多次余额不足，建议引导用户使用其他支付方式")
	}

	if count, exists := result.FailuresByCode["fraudulent"]; exists && count > 0 {
		recommendations = append(recommendations, "检测到欺诈交易，建议立即审查用户账户")
	}

	return recommendations
}

// GetFailureTrends 获取失败趋势分析
func (s *FailureAnalysisService) GetFailureTrends(ctx context.Context, userID string, timeRange TimeRange, interval string) (map[string][]TrendPoint, error) {
	var groupBy string
	switch interval {
	case "hour":
		groupBy = "DATE_FORMAT(last_occurred_at, '%Y-%m-%d %H:00:00')"
	case "day":
		groupBy = "DATE(last_occurred_at)"
	case "week":
		groupBy = "YEARWEEK(last_occurred_at)"
	case "month":
		groupBy = "DATE_FORMAT(last_occurred_at, '%Y-%m')"
	default:
		groupBy = "DATE(last_occurred_at)"
	}

	var results []struct {
		Period      string
		FailureType string
		Count       int
	}

	err := s.db.GetDB().Model(&models.PaymentFailureStats{}).
		Select(fmt.Sprintf("%s as period, failure_type, SUM(count) as count", groupBy)).
		Where("user_id = ? AND last_occurred_at BETWEEN ? AND ?",
			userID, timeRange.StartTime, timeRange.EndTime).
		Group("period, failure_type").
		Order("period").
		Scan(&results).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get failure trends: %w", err)
	}

	trends := make(map[string][]TrendPoint)
	for _, result := range results {
		if _, exists := trends[result.FailureType]; !exists {
			trends[result.FailureType] = []TrendPoint{}
		}
		trends[result.FailureType] = append(trends[result.FailureType], TrendPoint{
			Time:  result.Period,
			Value: result.Count,
		})
	}

	return trends, nil
}

// TrendPoint 趋势数据点
type TrendPoint struct {
	Time  string `json:"time"`
	Value int    `json:"value"`
}
