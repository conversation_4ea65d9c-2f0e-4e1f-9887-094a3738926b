package api

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"topnetwork.ai/topai/chat-webserver/analysis/service"
)

// PaymentAuditHandler 支付审计API处理器
type PaymentAuditHandler struct {
	analysisManager *service.AnalysisManager
}

// NewPaymentAuditHandler 创建支付审计API处理器
func NewPaymentAuditHandler(analysisManager *service.AnalysisManager) *PaymentAuditHandler {
	return &PaymentAuditHandler{
		analysisManager: analysisManager,
	}
}

// TimeRangeRequest 时间范围请求参数
type TimeRangeRequest struct {
	StartTime string `form:"start_time" binding:"required" example:"2024-01-01T00:00:00Z"`
	EndTime   string `form:"end_time" binding:"required" example:"2024-01-31T23:59:59Z"`
	Interval  string `form:"interval" example:"day"` // hour, day, week, month
}

// parseTimeRange 解析时间范围
func (h *PaymentAuditHandler) parseTimeRange(req TimeRangeRequest) (service.TimeRange, error) {
	startTime, err := time.Parse(time.RFC3339, req.StartTime)
	if err != nil {
		return service.TimeRange{}, err
	}

	endTime, err := time.Parse(time.RFC3339, req.EndTime)
	if err != nil {
		return service.TimeRange{}, err
	}

	return service.TimeRange{
		StartTime: startTime,
		EndTime:   endTime,
	}, nil
}

// GetUserPaymentAnalysis 获取用户支付分析
// @Summary 获取用户支付分析
// @Description 分析指定用户在指定时间范围内的支付数据，包括失败率、争议率、充值频率等
// @Tags 支付审计
// @Accept json
// @Produce json
// @Param user_id path string true "用户ID"
// @Param hours query int false "分析时间窗口（小时）" default(24)
// @Success 200 {object} service.UserAnalysisReport
// @Failure 400 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/v1/payment-audit/users/{user_id}/analysis [get]
func (h *PaymentAuditHandler) GetUserPaymentAnalysis(c *gin.Context) {
	userID := c.Param("user_id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "用户ID不能为空"})
		return
	}

	// 获取时间窗口参数，默认24小时
	hours := 24
	if hoursParam := c.Query("hours"); hoursParam != "" {
		if h, err := strconv.Atoi(hoursParam); err == nil && h > 0 {
			hours = h
		}
	}

	result, err := h.analysisManager.GetUserAnalysis(c.Request.Context(), userID, hours)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "分析失败: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, result)
}

// GetSystemPaymentAnalysis 获取系统级支付分析
// @Summary 获取系统级支付分析
// @Description 分析系统在指定时间范围内的整体支付数据
// @Tags 支付审计
// @Accept json
// @Produce json
// @Param hours query int false "分析时间窗口（小时）" default(24)
// @Success 200 {object} service.SystemAnalysisReport
// @Failure 400 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/v1/payment-audit/system/analysis [get]
func (h *PaymentAuditHandler) GetSystemPaymentAnalysis(c *gin.Context) {
	// 获取时间窗口参数，默认24小时
	hours := 24
	if hoursParam := c.Query("hours"); hoursParam != "" {
		if h, err := strconv.Atoi(hoursParam); err == nil && h > 0 {
			hours = h
		}
	}

	result, err := h.analysisManager.GetSystemAnalysis(c.Request.Context(), hours)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "分析失败: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, result)
}

// ManualAnalysis 手动触发用户分析
// @Summary 手动触发用户分析
// @Description 手动触发指定用户的支付分析
// @Tags 支付审计
// @Accept json
// @Produce json
// @Param user_id path string true "用户ID"
// @Param hours query int false "分析时间窗口（小时）" default(24)
// @Success 200 {object} service.UserAnalysisReport
// @Failure 400 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/v1/payment-audit/users/{user_id}/manual-analysis [post]
func (h *PaymentAuditHandler) ManualAnalysis(c *gin.Context) {
	userID := c.Param("user_id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "用户ID不能为空"})
		return
	}

	// 获取时间窗口参数，默认24小时
	hours := 24
	if hoursParam := c.Query("hours"); hoursParam != "" {
		if h, err := strconv.Atoi(hoursParam); err == nil && h > 0 {
			hours = h
		}
	}

	result, err := h.analysisManager.ManualAnalysis(c.Request.Context(), userID, hours)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "手动分析失败: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, result)
}

// @Summary 获取支付频率分析
// @Description 获取用户在指定时间范围内的支付频率分析
// @Tags 支付审计
// @Accept json
// @Produce json
// @Param user_id path string true "用户ID"
// @Param start_time query string true "开始时间" example(2024-01-01T00:00:00Z)
// @Param end_time query string true "结束时间" example(2024-01-31T23:59:59Z)
// @Param interval query string false "时间间隔" example(day) Enums(hour, day, week, month)
// @Success 200 {object} map[string]int
// @Failure 400 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/v1/payment-audit/users/{user_id}/frequency [get]
func (h *PaymentAuditHandler) GetPaymentFrequencyAnalysis(c *gin.Context) {
	userID := c.Param("user_id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "用户ID不能为空"})
		return
	}

	var req TimeRangeRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "参数错误: " + err.Error()})
		return
	}

	timeRange, err := h.parseTimeRange(req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "时间格式错误: " + err.Error()})
		return
	}

	interval := req.Interval
	if interval == "" {
		interval = "day"
	}

	result, err := h.paymentAuditService.GetPaymentFrequencyAnalysis(c.Request.Context(), userID, timeRange, interval)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取支付频率分析失败: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, result)
}

// GetFailureAnalysis 获取失败原因分析
// @Summary 获取失败原因分析
// @Description 获取用户在指定时间范围内的支付失败原因详细分析
// @Tags 支付审计
// @Accept json
// @Produce json
// @Param user_id path string true "用户ID"
// @Param start_time query string true "开始时间" example(2024-01-01T00:00:00Z)
// @Param end_time query string true "结束时间" example(2024-01-31T23:59:59Z)
// @Success 200 {object} service.FailureAnalysisResult
// @Failure 400 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/v1/payment-audit/users/{user_id}/failures [get]
func (h *PaymentAuditHandler) GetFailureAnalysis(c *gin.Context) {
	userID := c.Param("user_id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "用户ID不能为空"})
		return
	}

	var req TimeRangeRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "参数错误: " + err.Error()})
		return
	}

	timeRange, err := h.parseTimeRange(req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "时间格式错误: " + err.Error()})
		return
	}

	result, err := h.failureAnalysisService.AnalyzeFailures(c.Request.Context(), userID, timeRange)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "失败原因分析失败: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, result)
}

// GetFailureTrends 获取失败趋势分析
// @Summary 获取失败趋势分析
// @Description 获取用户在指定时间范围内的失败趋势分析
// @Tags 支付审计
// @Accept json
// @Produce json
// @Param user_id path string true "用户ID"
// @Param start_time query string true "开始时间" example(2024-01-01T00:00:00Z)
// @Param end_time query string true "结束时间" example(2024-01-31T23:59:59Z)
// @Param interval query string false "时间间隔" example(day) Enums(hour, day, week, month)
// @Success 200 {object} map[string][]service.TrendPoint
// @Failure 400 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/v1/payment-audit/users/{user_id}/failure-trends [get]
func (h *PaymentAuditHandler) GetFailureTrends(c *gin.Context) {
	userID := c.Param("user_id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "用户ID不能为空"})
		return
	}

	var req TimeRangeRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "参数错误: " + err.Error()})
		return
	}

	timeRange, err := h.parseTimeRange(req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "时间格式错误: " + err.Error()})
		return
	}

	interval := req.Interval
	if interval == "" {
		interval = "day"
	}

	result, err := h.failureAnalysisService.GetFailureTrends(c.Request.Context(), userID, timeRange, interval)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取失败趋势失败: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, result)
}

// GetGeoRiskAssessment 获取地理风险评估
// @Summary 获取地理风险评估
// @Description 获取用户在指定时间范围内的地理风险评估
// @Tags 支付审计
// @Accept json
// @Produce json
// @Param user_id path string true "用户ID"
// @Param start_time query string true "开始时间" example(2024-01-01T00:00:00Z)
// @Param end_time query string true "结束时间" example(2024-01-31T23:59:59Z)
// @Success 200 {object} service.GeoRiskAssessment
// @Failure 400 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/v1/payment-audit/users/{user_id}/geo-risk [get]
func (h *PaymentAuditHandler) GetGeoRiskAssessment(c *gin.Context) {
	userID := c.Param("user_id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "用户ID不能为空"})
		return
	}

	var req TimeRangeRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "参数错误: " + err.Error()})
		return
	}

	timeRange, err := h.parseTimeRange(req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "时间格式错误: " + err.Error()})
		return
	}

	result, err := h.geoRiskService.AssessGeoRisk(c.Request.Context(), userID, timeRange)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "地理风险评估失败: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, result)
}
