#!/usr/bin/env python3
"""
Unified AI Model Testing Framework
Provides comprehensive testing and performance comparison across multiple AI providers
"""

import asyncio
import statistics
import time
import json
import random
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from dataclasses import asdict
import logging
import numpy as np

try:
    from .base_ai_client import (
        BaseAIClient, CompletionRequest, CompletionResponse,
        ChatMessage, TestResult, PerformanceMetrics
    )
except ImportError:
    from base_ai_client import (
        BaseAIClient, CompletionRequest, CompletionResponse,
        ChatMessage, TestResult, PerformanceMetrics
    )

logger = logging.getLogger(__name__)

class UnifiedTestFramework:
    """Unified testing framework for multiple AI providers"""
    
    def __init__(self, clients: List[BaseAIClient]):
        self.clients = {client.provider_name: client for client in clients}
        self.test_cases = self._initialize_test_cases()
        
    def _initialize_test_cases(self) -> List[Dict[str, Any]]:
        """Initialize standard test cases"""
        return [
            {
                "name": "Simple Q&A",
                "messages": [ChatMessage(role="user", content="Please briefly introduce the history of artificial intelligence development.")],
                "max_tokens": 500,
                "category": "short"
            },
            {
                "name": "Code Generation",
                "messages": [ChatMessage(role="user", content="Please write a quicksort algorithm in Python with detailed comments.")],
                "max_tokens": 800,
                "category": "medium"
            },
            {
                "name": "Text Analysis",
                "messages": [ChatMessage(role="user", content="Please analyze the sentiment and main viewpoints of the following text: Machine learning is changing our world, from medical diagnosis to autonomous vehicles, AI technology applications are becoming increasingly widespread. However, we also need to pay attention to ethical issues and employment impacts that AI development may bring.")],
                "max_tokens": 600,
                "category": "medium"
            },
            {
                "name": "Creative Writing",
                "messages": [ChatMessage(role="user", content="Please write a science fiction short story about future cities, approximately 300 words.")],
                "max_tokens": 400,
                "category": "medium"
            },
            {
                "name": "Data Analysis",
                "messages": [ChatMessage(role="user", content="Suppose I have sales data from an e-commerce website: January sales of 1 million, February sales of 1.2 million, March sales of 950,000, April sales of 1.4 million. Please analyze the trends in this data and provide recommendations.")],
                "max_tokens": 500,
                "category": "medium"
            },
            {
                "name": "Complex Analysis",
                "messages": [ChatMessage(role="user", content="""Please provide a comprehensive analysis of the global economic impact of artificial intelligence over the next decade. 
                Consider the following aspects in your response:
                1. Job displacement and creation across different sectors
                2. Productivity gains and economic growth potential
                3. Regional differences in AI adoption and economic benefits
                4. Policy recommendations for governments to manage the transition
                5. The role of education and reskilling programs
                Please provide detailed explanations and cite relevant examples.""")],
                "max_tokens": 1000,
                "category": "long"
            }
        ]
    
    def generate_random_test_case(self) -> Dict[str, Any]:
        """Generate a random test case"""
        return random.choice(self.test_cases)
    
    async def run_single_test(self, client: BaseAIClient, model: str, test_case: Dict[str, Any], 
                             stream: bool) -> TestResult:
        """Run a single test on a specific client and model"""
        request = CompletionRequest(
            messages=test_case["messages"],
            model=model,
            max_tokens=test_case["max_tokens"],
            stream=stream
        )
        
        try:
            if stream:
                result = await client.test_streaming_performance(request)
            else:
                result = await client.test_non_streaming_performance(request)
            
            # Add test case metadata
            result.prompt_category = test_case["category"]
            result.prompt_length = len(test_case["messages"][0].content)
            
            return result
            
        except Exception as e:
            return TestResult(
                test_name=f"{'stream' if stream else 'non_stream'}_{model.split('/')[-1]}",
                success=False,
                response_time=0,
                tokens_generated=0,
                error=str(e),
                model_name=model,
                provider=client.provider_name,
                prompt_category=test_case["category"],
                prompt_length=len(test_case["messages"][0].content)
            )
    
    async def run_batch_test(self, client: BaseAIClient, model: str, test_case: Dict[str, Any], 
                           stream: bool, batch_size: int = 5) -> List[TestResult]:
        """Run batch tests for a specific configuration"""
        logger.info(f"Running batch test: {client.provider_name} - {model} - {test_case['name']} - {'streaming' if stream else 'non-streaming'} - batch size: {batch_size}")
        
        tasks = []
        for i in range(batch_size):
            task = self.run_single_test(client, model, test_case, stream)
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process exception results
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append(TestResult(
                    test_name=f"{'stream' if stream else 'non_stream'}_{model.split('/')[-1]}",
                    success=False,
                    response_time=0,
                    tokens_generated=0,
                    error=str(result),
                    model_name=model,
                    provider=client.provider_name,
                    prompt_category=test_case["category"]
                ))
            else:
                processed_results.append(result)
        
        return processed_results
    
    def calculate_metrics(self, results: List[TestResult]) -> PerformanceMetrics:
        """Calculate performance metrics from test results"""
        if not results:
            return PerformanceMetrics(0, 0, 0, 0, 0, 0, None, 0, 0, 0, 0, 0, 0, 0, 0, 0)
        
        successful_results = [r for r in results if r.success]
        
        if not successful_results:
            return PerformanceMetrics(0, 0, 0, 0, 0, 0, None, 0, 0, 0, 0, 0, 0, 0, 0, 0)
        
        response_times = [r.response_time for r in successful_results]
        total_tokens = sum(r.tokens_generated for r in successful_results)
        total_time = sum(response_times)
        
        # Calculate first token time average (for streaming requests only)
        first_token_times = [r.first_token_time for r in successful_results if r.first_token_time is not None]
        avg_first_token_time = statistics.mean(first_token_times) if first_token_times else None
        
        # Calculate percentiles
        sorted_times = sorted(response_times)
        sorted_first_token_times = sorted(first_token_times) if first_token_times else None
        p50_first_token_time=np.percentile(sorted_first_token_times, 50) if first_token_times else None
        p90_first_token_time=np.percentile(sorted_first_token_times, 90) if first_token_times else None
        p95_first_token_time=np.percentile(sorted_first_token_times, 95) if first_token_times else None
        p99_first_token_time=np.percentile(sorted_first_token_times, 99) if first_token_times else None
        return PerformanceMetrics(
            avg_response_time=statistics.mean(response_times),
            min_response_time=min(response_times),
            max_response_time=max(response_times),
            success_rate=len(successful_results) / len(results),
            total_tokens=total_tokens,
            tokens_per_second=total_tokens / total_time if total_time > 0 else 0,
            avg_first_token_time=avg_first_token_time,
            p50_first_token_time=p50_first_token_time,
            p90_first_token_time=p90_first_token_time,
            p95_first_token_time=p95_first_token_time,
            p99_first_token_time=p99_first_token_time,

            p50_response_time=np.percentile(sorted_times, 50),
            p90_response_time=np.percentile(sorted_times, 90),
            p95_response_time=np.percentile(sorted_times, 95),
            p99_response_time=np.percentile(sorted_times, 99),
            std_dev=statistics.stdev(response_times) if len(response_times) > 1 else 0
        )
    
    def get_common_models(self) -> List[str]:
        """Get models that are supported by all clients"""
        if not self.clients:
            return []
        
        # Get intersection of all supported models
        all_models = [set(client.get_supported_models().keys()) for client in self.clients.values()]
        common_models = set.intersection(*all_models) if all_models else set()
        
        return list(common_models)
    
    def get_all_models_by_provider(self) -> Dict[str, List[str]]:
        """Get all models grouped by provider"""
        return {
            provider: list(client.get_supported_models().keys())
            for provider, client in self.clients.items()
        }

    async def run_comprehensive_test(self, models_to_test: Optional[List[str]] = None,
                                   batch_size: int = 5, test_both_modes: bool = True) -> Dict[str, Any]:
        """Run comprehensive tests across all clients and models"""
        logger.info("Starting comprehensive AI model performance test")

        if models_to_test is None:
            # Use common models if available, otherwise test all models
            common_models = self.get_common_models()
            if common_models:
                models_to_test = common_models
                logger.info(f"Testing common models: {models_to_test}")
            else:
                # Test all models from all providers
                all_models = self.get_all_models_by_provider()
                models_to_test = []
                for provider, models in all_models.items():
                    models_to_test.extend(models)
                logger.info(f"No common models found, testing all available models")

        all_results = {}

        for provider_name, client in self.clients.items():
            logger.info(f"Testing provider: {provider_name}")
            provider_results = {}

            # Filter models supported by this client
            client_models = [m for m in models_to_test if client.is_model_supported(m)]

            for model in client_models:
                logger.info(f"  Testing model: {model}")
                model_results = {}

                for test_case in self.test_cases:
                    logger.info(f"    Test case: {test_case['name']}")

                    test_case_results = {}

                    if test_both_modes:
                        # Streaming test
                        stream_results = await self.run_batch_test(
                            client, model, test_case, stream=True, batch_size=batch_size
                        )
                        stream_metrics = self.calculate_metrics(stream_results)
                        test_case_results['streaming'] = {
                            'results': stream_results,
                            'metrics': stream_metrics
                        }

                        # Add delay to avoid rate limiting
                        await asyncio.sleep(1)

                        # Non-streaming test
                        non_stream_results = await self.run_batch_test(
                            client, model, test_case, stream=False, batch_size=batch_size
                        )
                        non_stream_metrics = self.calculate_metrics(non_stream_results)
                        test_case_results['non_streaming'] = {
                            'results': non_stream_results,
                            'metrics': non_stream_metrics
                        }
                    else:
                        # Only streaming test
                        stream_results = await self.run_batch_test(
                            client, model, test_case, stream=True, batch_size=batch_size
                        )
                        stream_metrics = self.calculate_metrics(stream_results)
                        test_case_results['streaming'] = {
                            'results': stream_results,
                            'metrics': stream_metrics
                        }

                    model_results[test_case['name']] = test_case_results

                    # Add delay between test cases
                    await asyncio.sleep(2)

                provider_results[model] = model_results

            all_results[provider_name] = provider_results

        return all_results

    def generate_comparison_report(self, results: Dict[str, Any]) -> str:
        """Generate a comprehensive comparison report"""
        report_lines = []
        report_lines.append("=" * 80)
        report_lines.append("AI MODEL PERFORMANCE COMPARISON REPORT")
        report_lines.append("=" * 80)
        report_lines.append(f"Test Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append(f"Providers Tested: {list(results.keys())}")
        report_lines.append("")

        # Collect performance data for comparison
        comparison_data = []

        for provider, provider_results in results.items():
            for model, model_results in provider_results.items():
                for test_case, test_results in model_results.items():
                    if 'streaming' in test_results and 'non_streaming' in test_results:
                        stream_metrics = test_results['streaming']['metrics']
                        non_stream_metrics = test_results['non_streaming']['metrics']

                        if stream_metrics.success_rate > 0 and non_stream_metrics.success_rate > 0:
                            comparison_data.append({
                                'provider': provider,
                                'model': model,
                                'test_case': test_case,
                                'stream_avg_time': stream_metrics.avg_response_time,
                                'non_stream_avg_time': non_stream_metrics.avg_response_time,
                                'stream_tokens_per_sec': stream_metrics.tokens_per_second,
                                'non_stream_tokens_per_sec': non_stream_metrics.tokens_per_second,
                                'stream_success_rate': stream_metrics.success_rate,
                                'non_stream_success_rate': non_stream_metrics.success_rate,
                                'first_token_time': stream_metrics.avg_first_token_time
                            })

        if comparison_data:
            # Overall performance ranking
            report_lines.append("OVERALL PERFORMANCE RANKING (by streaming response time)")
            report_lines.append("-" * 60)

            # Sort by streaming performance
            sorted_data = sorted(comparison_data, key=lambda x: x['stream_avg_time'])

            for i, data in enumerate(sorted_data[:10]):  # Top 10
                report_lines.append(f"{i+1:2d}. {data['provider']:12} {data['model']:20} "
                                  f"{data['test_case']:15} {data['stream_avg_time']:6.2f}s")

            report_lines.append("")

            # Provider comparison summary
            provider_stats = {}
            for data in comparison_data:
                provider = data['provider']
                if provider not in provider_stats:
                    provider_stats[provider] = {
                        'stream_times': [],
                        'non_stream_times': [],
                        'stream_throughput': [],
                        'non_stream_throughput': [],
                        'success_rates': []
                    }

                provider_stats[provider]['stream_times'].append(data['stream_avg_time'])
                provider_stats[provider]['non_stream_times'].append(data['non_stream_avg_time'])
                provider_stats[provider]['stream_throughput'].append(data['stream_tokens_per_sec'])
                provider_stats[provider]['non_stream_throughput'].append(data['non_stream_tokens_per_sec'])
                provider_stats[provider]['success_rates'].append(data['stream_success_rate'])

            report_lines.append("PROVIDER COMPARISON SUMMARY")
            report_lines.append("-" * 60)
            report_lines.append(f"{'Provider':<12} {'Avg Stream':<12} {'Avg Non-Stream':<15} {'Avg Throughput':<15} {'Success Rate':<12}")
            report_lines.append("-" * 60)

            for provider, stats in provider_stats.items():
                avg_stream = statistics.mean(stats['stream_times'])
                avg_non_stream = statistics.mean(stats['non_stream_times'])
                avg_throughput = statistics.mean(stats['stream_throughput'])
                avg_success = statistics.mean(stats['success_rates'])

                report_lines.append(f"{provider:<12} {avg_stream:<12.2f} {avg_non_stream:<15.2f} "
                                  f"{avg_throughput:<15.1f} {avg_success:<12.1%}")

        report_lines.append("=" * 80)

        return "\n".join(report_lines)

    def save_results(self, results: Dict[str, Any], filename: Optional[str] = None) -> str:
        """Save test results to JSON file"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"unified_ai_test_results_{timestamp}.json"

        # Convert results to serializable format
        serializable_results = {}
        for provider, provider_results in results.items():
            serializable_results[provider] = {}
            for model, model_results in provider_results.items():
                serializable_results[provider][model] = {}
                for test_case, test_results in model_results.items():
                    serializable_results[provider][model][test_case] = {}
                    for mode, mode_results in test_results.items():
                        serializable_results[provider][model][test_case][mode] = {
                            'metrics': asdict(mode_results['metrics']),
                            'result_count': len(mode_results['results']),
                            'successful_results': len([r for r in mode_results['results'] if r.success])
                        }

        # Add metadata
        final_results = {
            'timestamp': datetime.now().isoformat(),
            'test_summary': {
                'providers_tested': list(results.keys()),
                'total_test_cases': len(self.test_cases),
                'framework_version': '1.0.0'
            },
            'results': serializable_results
        }

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(final_results, f, ensure_ascii=False, indent=2)

        return filename

    def print_detailed_results(self, results: Dict[str, Any]):
        """Print detailed test results"""
        print("\n" + "=" * 80)
        print("DETAILED AI MODEL PERFORMANCE TEST RESULTS")
        print("=" * 80)
        print(f"Test Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        for provider, provider_results in results.items():
            print(f"\n{'=' * 60}")
            print(f"PROVIDER: {provider.upper()}")
            print(f"{'=' * 60}")

            for model, model_results in provider_results.items():
                print(f"\nModel: {model}")
                print("-" * 40)

                for test_case, test_results in model_results.items():
                    print(f"\nTest Case: {test_case}")
                    print("." * 30)

                    for mode, mode_results in test_results.items():
                        metrics = mode_results['metrics']
                        print(f"\n{mode.upper()}:")
                        print(f"  Success Rate: {metrics.success_rate:.1%}")
                        print(f"  Avg Response Time: {metrics.avg_response_time:.2f}s")
                        if metrics.avg_first_token_time:
                            print(f"  Avg First Token Time: {metrics.avg_first_token_time:.2f}s")
                            print(f"  P50 First Token Time: {metrics.p50_first_token_time:.2f}s")
                            print(f"  P95 First Token Time: {metrics.p95_first_token_time:.2f}s")
                            
                        print(f"  Tokens/Second: {metrics.tokens_per_second:.1f}")
                        print(f"  p50 Response Time: {metrics.p50_response_time:.2f}s")
                        print(f"  P95 Response Time: {metrics.p95_response_time:.2f}s")
                        print(f"  Standard Deviation: {metrics.std_dev:.2f}s")

        print("\n" + "=" * 80)
