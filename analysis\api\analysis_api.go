package api

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"topnetwork.ai/topai/chat-webserver/analysis/service"
)

// AnalysisAPIHandler 分析API处理器
type AnalysisAPIHandler struct {
	analysisManager *service.AnalysisManager
}

// NewAnalysisAPIHandler 创建分析API处理器
func NewAnalysisAPIHandler(analysisManager *service.AnalysisManager) *AnalysisAPIHandler {
	return &AnalysisAPIHandler{
		analysisManager: analysisManager,
	}
}

// GetUserAnalysis 获取用户分析报告
// @Summary 获取用户分析报告
// @Description 获取指定用户的完整支付分析报告，包括支付失败率、争议率、充值频率、银行卡使用统计和失败原因分析
// @Tags 支付分析
// @Accept json
// @Produce json
// @Param user_id path string true "用户ID"
// @Param hours query int false "分析时间窗口（小时）" default(24)
// @Success 200 {object} service.UserAnalysisReport
// @Failure 400 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/v1/analysis/users/{user_id} [get]
func (h *AnalysisAPIHandler) GetUserAnalysis(c *gin.Context) {
	userID := c.Param("user_id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "用户ID不能为空"})
		return
	}

	// 获取时间窗口参数，默认24小时
	hours := 24
	if hoursParam := c.Query("hours"); hoursParam != "" {
		if h, err := strconv.Atoi(hoursParam); err == nil && h > 0 {
			hours = h
		}
	}

	result, err := h.analysisManager.GetUserAnalysis(c.Request.Context(), userID, hours)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取用户分析失败: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, result)
}

// GetSystemAnalysis 获取系统级分析报告
// @Summary 获取系统级分析报告
// @Description 获取系统整体的支付分析报告，包括总体指标、高风险用户等
// @Tags 支付分析
// @Accept json
// @Produce json
// @Param hours query int false "分析时间窗口（小时）" default(24)
// @Success 200 {object} service.SystemAnalysisReport
// @Failure 400 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/v1/analysis/system [get]
func (h *AnalysisAPIHandler) GetSystemAnalysis(c *gin.Context) {
	// 获取时间窗口参数，默认24小时
	hours := 24
	if hoursParam := c.Query("hours"); hoursParam != "" {
		if h, err := strconv.Atoi(hoursParam); err == nil && h > 0 {
			hours = h
		}
	}

	result, err := h.analysisManager.GetSystemAnalysis(c.Request.Context(), hours)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取系统分析失败: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, result)
}

// ManualAnalysis 手动触发用户分析
// @Summary 手动触发用户分析
// @Description 手动触发指定用户的支付分析，立即返回分析结果
// @Tags 支付分析
// @Accept json
// @Produce json
// @Param user_id path string true "用户ID"
// @Param hours query int false "分析时间窗口（小时）" default(24)
// @Success 200 {object} service.UserAnalysisReport
// @Failure 400 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/v1/analysis/users/{user_id}/manual [post]
func (h *AnalysisAPIHandler) ManualAnalysis(c *gin.Context) {
	userID := c.Param("user_id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "用户ID不能为空"})
		return
	}

	// 获取时间窗口参数，默认24小时
	hours := 24
	if hoursParam := c.Query("hours"); hoursParam != "" {
		if h, err := strconv.Atoi(hoursParam); err == nil && h > 0 {
			hours = h
		}
	}

	result, err := h.analysisManager.ManualAnalysis(c.Request.Context(), userID, hours)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "手动分析失败: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, result)
}

// AnalysisStatus 分析状态
type AnalysisStatus struct {
	IsRunning    bool   `json:"is_running"`
	LastRunTime  string `json:"last_run_time,omitempty"`
	NextRunTime  string `json:"next_run_time,omitempty"`
	Status       string `json:"status"`
	Message      string `json:"message,omitempty"`
}

// GetAnalysisStatus 获取分析服务状态
// @Summary 获取分析服务状态
// @Description 获取定时分析服务的运行状态
// @Tags 支付分析
// @Accept json
// @Produce json
// @Success 200 {object} AnalysisStatus
// @Router /api/v1/analysis/status [get]
func (h *AnalysisAPIHandler) GetAnalysisStatus(c *gin.Context) {
	// 这里可以添加实际的状态检查逻辑
	status := AnalysisStatus{
		IsRunning: true,
		Status:    "running",
		Message:   "分析服务正在正常运行",
	}

	c.JSON(http.StatusOK, status)
}

// StartAnalysis 启动分析服务
// @Summary 启动分析服务
// @Description 启动定时分析服务
// @Tags 支付分析
// @Accept json
// @Produce json
// @Success 200 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/v1/analysis/start [post]
func (h *AnalysisAPIHandler) StartAnalysis(c *gin.Context) {
	err := h.analysisManager.StartAnalysis(c.Request.Context())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "启动分析服务失败: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "分析服务启动成功"})
}

// StopAnalysis 停止分析服务
// @Summary 停止分析服务
// @Description 停止定时分析服务
// @Tags 支付分析
// @Accept json
// @Produce json
// @Success 200 {object} map[string]string
// @Router /api/v1/analysis/stop [post]
func (h *AnalysisAPIHandler) StopAnalysis(c *gin.Context) {
	h.analysisManager.StopAnalysis()
	c.JSON(http.StatusOK, gin.H{"message": "分析服务停止成功"})
}

// HealthCheck 健康检查
// @Summary 健康检查
// @Description 检查分析服务的健康状态
// @Tags 支付分析
// @Accept json
// @Produce json
// @Success 200 {object} map[string]string
// @Router /api/v1/analysis/health [get]
func (h *AnalysisAPIHandler) HealthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":  "ok",
		"service": "payment-analysis",
		"version": "1.0.0",
	})
}

// RegisterRoutes 注册路由
func (h *AnalysisAPIHandler) RegisterRoutes(r *gin.Engine) {
	v1 := r.Group("/api/v1/analysis")
	{
		// 用户分析相关
		v1.GET("/users/:user_id", h.GetUserAnalysis)
		v1.POST("/users/:user_id/manual", h.ManualAnalysis)
		
		// 系统分析相关
		v1.GET("/system", h.GetSystemAnalysis)
		
		// 服务管理相关
		v1.GET("/status", h.GetAnalysisStatus)
		v1.POST("/start", h.StartAnalysis)
		v1.POST("/stop", h.StopAnalysis)
		v1.GET("/health", h.HealthCheck)
	}
}
