package api

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"topnetwork.ai/topai/chat-webserver/analysis/repository"
	"topnetwork.ai/topai/chat-webserver/analysis/service"
)

// AnalysisAPIHandler 分析API处理器
type AnalysisAPIHandler struct {
	analysisManager *service.AnalysisManager
	stripeEventDAO  *repository.StripeEventDAO
}

// NewAnalysisAPIHandler 创建分析API处理器
func NewAnalysisAPIHandler(analysisManager *service.AnalysisManager, stripeEventDAO *repository.StripeEventDAO) *AnalysisAPIHandler {
	return &AnalysisAPIHandler{
		analysisManager: analysisManager,
		stripeEventDAO:  stripeEventDAO,
	}
}

// GetUserAnalysis 获取用户分析报告
// @Summary 获取用户分析报告
// @Description 获取指定用户的完整支付分析报告，包括支付失败率、争议率、充值频率、银行卡使用统计和失败原因分析
// @Tags 支付分析
// @Accept json
// @Produce json
// @Param user_id path string true "用户ID"
// @Param hours query int false "分析时间窗口（小时）" default(24)
// @Success 200 {object} service.UserAnalysisReport
// @Failure 400 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/v1/analysis/users/{user_id} [get]
func (h *AnalysisAPIHandler) GetUserAnalysis(c *gin.Context) {
	userID := c.Param("user_id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "用户ID不能为空"})
		return
	}

	// 获取时间窗口参数，默认24小时
	hours := 24
	if hoursParam := c.Query("hours"); hoursParam != "" {
		if h, err := strconv.Atoi(hoursParam); err == nil && h > 0 {
			hours = h
		}
	}

	result, err := h.analysisManager.GetUserAnalysis(c.Request.Context(), userID, hours)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取用户分析失败: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, result)
}

// GetSystemAnalysis 获取系统级分析报告
// @Summary 获取系统级分析报告
// @Description 获取系统整体的支付分析报告，包括总体指标、高风险用户等
// @Tags 支付分析
// @Accept json
// @Produce json
// @Param hours query int false "分析时间窗口（小时）" default(24)
// @Success 200 {object} service.SystemAnalysisReport
// @Failure 400 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/v1/analysis/system [get]
func (h *AnalysisAPIHandler) GetSystemAnalysis(c *gin.Context) {
	// 获取时间窗口参数，默认24小时
	hours := 24
	if hoursParam := c.Query("hours"); hoursParam != "" {
		if h, err := strconv.Atoi(hoursParam); err == nil && h > 0 {
			hours = h
		}
	}

	result, err := h.analysisManager.GetSystemAnalysis(c.Request.Context(), hours)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取系统分析失败: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, result)
}

// ManualAnalysis 手动触发用户分析
// @Summary 手动触发用户分析
// @Description 手动触发指定用户的支付分析，立即返回分析结果
// @Tags 支付分析
// @Accept json
// @Produce json
// @Param user_id path string true "用户ID"
// @Param hours query int false "分析时间窗口（小时）" default(24)
// @Success 200 {object} service.UserAnalysisReport
// @Failure 400 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/v1/analysis/users/{user_id}/manual [post]
func (h *AnalysisAPIHandler) ManualAnalysis(c *gin.Context) {
	userID := c.Param("user_id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "用户ID不能为空"})
		return
	}

	// 获取时间窗口参数，默认24小时
	hours := 24
	if hoursParam := c.Query("hours"); hoursParam != "" {
		if h, err := strconv.Atoi(hoursParam); err == nil && h > 0 {
			hours = h
		}
	}

	result, err := h.analysisManager.ManualAnalysis(c.Request.Context(), userID, hours)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "手动分析失败: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, result)
}

// AnalysisStatus 分析状态
type AnalysisStatus struct {
	IsRunning   bool   `json:"is_running"`
	LastRunTime string `json:"last_run_time,omitempty"`
	NextRunTime string `json:"next_run_time,omitempty"`
	Status      string `json:"status"`
	Message     string `json:"message,omitempty"`
}

// GetAnalysisStatus 获取分析服务状态
// @Summary 获取分析服务状态
// @Description 获取定时分析服务的运行状态
// @Tags 支付分析
// @Accept json
// @Produce json
// @Success 200 {object} AnalysisStatus
// @Router /api/v1/analysis/status [get]
func (h *AnalysisAPIHandler) GetAnalysisStatus(c *gin.Context) {
	// 这里可以添加实际的状态检查逻辑
	status := AnalysisStatus{
		IsRunning: true,
		Status:    "running",
		Message:   "分析服务正在正常运行",
	}

	c.JSON(http.StatusOK, status)
}

// StartAnalysis 启动分析服务
// @Summary 启动分析服务
// @Description 启动定时分析服务
// @Tags 支付分析
// @Accept json
// @Produce json
// @Success 200 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/v1/analysis/start [post]
func (h *AnalysisAPIHandler) StartAnalysis(c *gin.Context) {
	err := h.analysisManager.StartAnalysis(c.Request.Context())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "启动分析服务失败: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "分析服务启动成功"})
}

// StopAnalysis 停止分析服务
// @Summary 停止分析服务
// @Description 停止定时分析服务
// @Tags 支付分析
// @Accept json
// @Produce json
// @Success 200 {object} map[string]string
// @Router /api/v1/analysis/stop [post]
func (h *AnalysisAPIHandler) StopAnalysis(c *gin.Context) {
	h.analysisManager.StopAnalysis()
	c.JSON(http.StatusOK, gin.H{"message": "分析服务停止成功"})
}

// HealthCheck 健康检查
// @Summary 健康检查
// @Description 检查分析服务的健康状态
// @Tags 支付分析
// @Accept json
// @Produce json
// @Success 200 {object} map[string]string
// @Router /api/v1/analysis/health [get]
func (h *AnalysisAPIHandler) HealthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":  "ok",
		"service": "payment-analysis",
		"version": "1.0.0",
	})
}

// GetStripeEvents 分页查询Stripe事件
// @Summary 分页查询Stripe事件
// @Description 支持多种过滤条件的分页查询Stripe事件
// @Tags Stripe事件
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页大小" default(20)
// @Param event_types query []string false "事件类型过滤"
// @Param start_time query string false "开始时间" format(date-time)
// @Param end_time query string false "结束时间" format(date-time)
// @Param processed query bool false "处理状态"
// @Param customer_id query string false "客户ID"
// @Param event_id_contains query string false "事件ID包含"
// @Success 200 {object} repository.PaginatedStripeEventsResult
// @Failure 400 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/v1/analysis/stripe-events [get]
func (h *AnalysisAPIHandler) GetStripeEvents(c *gin.Context) {
	// 解析分页参数
	var params repository.PaginationParams
	if err := c.ShouldBindQuery(&params); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "分页参数错误: " + err.Error()})
		return
	}

	// 解析过滤参数
	var filters repository.StripeEventFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "过滤参数错误: " + err.Error()})
		return
	}

	// 解析时间参数
	if startTimeStr := c.Query("start_time"); startTimeStr != "" {
		if startTime, err := time.Parse(time.RFC3339, startTimeStr); err == nil {
			filters.StartTime = &startTime
		}
	}
	if endTimeStr := c.Query("end_time"); endTimeStr != "" {
		if endTime, err := time.Parse(time.RFC3339, endTimeStr); err == nil {
			filters.EndTime = &endTime
		}
	}

	// 执行分页查询
	result, err := h.stripeEventDAO.GetStripeEventsPaginated(params, filters)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "查询失败: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, result)
}

// GetUserStripeEvents 分页查询用户Stripe事件
// @Summary 分页查询用户Stripe事件
// @Description 查询指定用户的Stripe事件，支持分页和过滤
// @Tags Stripe事件
// @Accept json
// @Produce json
// @Param user_id path string true "用户ID"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页大小" default(20)
// @Param event_types query []string false "事件类型过滤"
// @Param start_time query string false "开始时间" format(date-time)
// @Param end_time query string false "结束时间" format(date-time)
// @Param processed query bool false "处理状态"
// @Success 200 {object} repository.PaginatedStripeEventsResult
// @Failure 400 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/v1/analysis/users/{user_id}/stripe-events [get]
func (h *AnalysisAPIHandler) GetUserStripeEvents(c *gin.Context) {
	userID := c.Param("user_id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "用户ID不能为空"})
		return
	}

	// 解析分页参数
	var params repository.PaginationParams
	if err := c.ShouldBindQuery(&params); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "分页参数错误: " + err.Error()})
		return
	}

	// 解析过滤参数
	var filters repository.StripeEventFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "过滤参数错误: " + err.Error()})
		return
	}

	// 解析时间参数
	if startTimeStr := c.Query("start_time"); startTimeStr != "" {
		if startTime, err := time.Parse(time.RFC3339, startTimeStr); err == nil {
			filters.StartTime = &startTime
		}
	}
	if endTimeStr := c.Query("end_time"); endTimeStr != "" {
		if endTime, err := time.Parse(time.RFC3339, endTimeStr); err == nil {
			filters.EndTime = &endTime
		}
	}

	// 执行用户事件分页查询
	result, err := h.stripeEventDAO.GetUserEventsPaginated(userID, params, filters)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "查询用户事件失败: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, result)
}

// GetUnprocessedStripeEvents 分页查询未处理的Stripe事件
// @Summary 分页查询未处理的Stripe事件
// @Description 查询所有未处理的Stripe事件，支持分页
// @Tags Stripe事件
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页大小" default(20)
// @Success 200 {object} repository.PaginatedStripeEventsResult
// @Failure 400 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/v1/analysis/stripe-events/unprocessed [get]
func (h *AnalysisAPIHandler) GetUnprocessedStripeEvents(c *gin.Context) {
	// 解析分页参数
	var params repository.PaginationParams
	if err := c.ShouldBindQuery(&params); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "分页参数错误: " + err.Error()})
		return
	}

	// 执行未处理事件分页查询
	result, err := h.stripeEventDAO.GetUnprocessedEventsPaginated(params)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "查询未处理事件失败: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, result)
}

// RegisterRoutes 注册路由
func (h *AnalysisAPIHandler) RegisterRoutes(r *gin.Engine) {
	v1 := r.Group("/api/v1/analysis")
	{
		// 用户分析相关
		v1.GET("/users/:user_id", h.GetUserAnalysis)
		v1.POST("/users/:user_id/manual", h.ManualAnalysis)
		v1.GET("/users/:user_id/stripe-events", h.GetUserStripeEvents)

		// 系统分析相关
		v1.GET("/system", h.GetSystemAnalysis)

		// Stripe事件查询相关
		v1.GET("/stripe-events", h.GetStripeEvents)
		v1.GET("/stripe-events/unprocessed", h.GetUnprocessedStripeEvents)

		// 服务管理相关
		v1.GET("/status", h.GetAnalysisStatus)
		v1.POST("/start", h.StartAnalysis)
		v1.POST("/stop", h.StopAnalysis)
		v1.GET("/health", h.HealthCheck)
	}
}
