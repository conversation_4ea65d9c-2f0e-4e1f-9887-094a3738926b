#!/usr/bin/env python3
"""
Base AI Client Interface
Defines the abstract interface for AI model clients with unified data structures
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import List, Dict, Any, Optional, AsyncGenerator, Union
from datetime import datetime
import time
import logging

logger = logging.getLogger(__name__)

@dataclass
class TestResult:
    """Unified test result data structure"""
    test_name: str
    success: bool
    response_time: float
    tokens_generated: int
    error: Optional[str] = None
    first_token_time: Optional[float] = None  # Time to first token for streaming requests
    completion_time: Optional[float] = None
    model_name: Optional[str] = None
    prompt_category: Optional[str] = None
    prompt_length: Optional[int] = None
    provider: Optional[str] = None  # e.g., "openrouter", "bedrock"

@dataclass
class PerformanceMetrics:
    """Performance metrics statistics"""
    avg_response_time: float
    min_response_time: float
    max_response_time: float
    success_rate: float
    total_tokens: int
    tokens_per_second: float
    avg_first_token_time: Optional[float] = None
    p50_first_token_time: Optional[float] = None
    p90_first_token_time: Optional[float] = None
    p95_first_token_time: Optional[float] = None
    p99_first_token_time: Optional[float] = None
    p50_response_time: Optional[float] = None
    p90_response_time: Optional[float] = None
    p95_response_time: Optional[float] = None
    p99_response_time: Optional[float] = None
    std_dev: Optional[float] = None

@dataclass
class ModelConfig:
    """Model configuration data structure"""
    model_id: str
    display_name: str
    max_tokens: int
    supports_streaming: bool = True
    provider_specific_config: Optional[Dict[str, Any]] = None

@dataclass
class ChatMessage:
    """Unified chat message structure"""
    role: str  # "user", "assistant", "system"
    content: str

@dataclass
class CompletionRequest:
    """Unified completion request structure"""
    messages: List[ChatMessage]
    model: str
    max_tokens: int = 1000
    temperature: float = 0.7
    top_p: float = 0.9
    stream: bool = False
    additional_params: Optional[Dict[str, Any]] = None

@dataclass
class CompletionResponse:
    """Unified completion response structure"""
    content: str
    model: str
    tokens_used: int
    finish_reason: Optional[str] = None
    provider_specific_data: Optional[Dict[str, Any]] = None

class BaseAIClient(ABC):
    """Abstract base class for AI model clients"""
    
    def __init__(self, provider_name: str):
        self.provider_name = provider_name
        self.supported_models: Dict[str, ModelConfig] = {}
        self._initialize_models()
    
    @abstractmethod
    def _initialize_models(self) -> None:
        """Initialize the supported models configuration"""
        pass
    
    @abstractmethod
    async def create_completion_stream(self, request: CompletionRequest) -> AsyncGenerator[str, None]:
        """
        Create a streaming completion
        
        Args:
            request: Completion request parameters
            
        Yields:
            Content chunks as they arrive
        """
        pass
    
    @abstractmethod
    async def create_completion_non_stream(self, request: CompletionRequest) -> CompletionResponse:
        """
        Create a non-streaming completion
        
        Args:
            request: Completion request parameters
            
        Returns:
            Complete response
        """
        pass
    
    async def test_streaming_performance(self, request: CompletionRequest) -> TestResult:
        """
        Test streaming performance for a given request
        
        Args:
            request: Completion request parameters
            
        Returns:
            Test result with performance metrics
        """
        test_name = f"stream_{request.model.split('/')[-1]}"
        start_time = time.time()
        first_token_time = None
        tokens_generated = 0
        full_response = ""
        
        try:
            async for chunk in self.create_completion_stream(request):
                if first_token_time is None:
                    first_token_time = time.time() - start_time
                full_response += chunk
                tokens_generated += len(chunk.split())
            
            end_time = time.time()
            return TestResult(
                test_name=test_name,
                success=True,
                response_time=end_time - start_time,
                tokens_generated=tokens_generated,
                first_token_time=first_token_time,
                completion_time=end_time - start_time,
                model_name=request.model,
                provider=self.provider_name
            )
            
        except Exception as e:
            print(e)
            return TestResult(
                test_name=test_name,
                success=False,
                response_time=time.time() - start_time,
                tokens_generated=0,
                error=str(e),
                model_name=request.model,
                provider=self.provider_name
            )
    
    async def test_non_streaming_performance(self, request: CompletionRequest) -> TestResult:
        """
        Test non-streaming performance for a given request
        
        Args:
            request: Completion request parameters
            
        Returns:
            Test result with performance metrics
        """
        test_name = f"non_stream_{request.model.split('/')[-1]}"
        start_time = time.time()
        
        try:
            response = await self.create_completion_non_stream(request)
            end_time = time.time()
            
            return TestResult(
                test_name=test_name,
                success=True,
                response_time=end_time - start_time,
                tokens_generated=response.tokens_used,
                model_name=request.model,
                provider=self.provider_name
            )
            
        except Exception as e:
            print(e)
            return TestResult(
                test_name=test_name,
                success=False,
                response_time=time.time() - start_time,
                tokens_generated=0,
                error=str(e),
                model_name=request.model,
                provider=self.provider_name
            )
    
    def get_supported_models(self) -> Dict[str, ModelConfig]:
        """Get list of supported models"""
        return self.supported_models
    
    def is_model_supported(self, model_name: str) -> bool:
        """Check if a model is supported"""
        return model_name in self.supported_models
    
    def get_model_config(self, model_name: str) -> Optional[ModelConfig]:
        """Get configuration for a specific model"""
        return self.supported_models.get(model_name)
    
    @abstractmethod
    def _prepare_request(self, request: CompletionRequest) -> Any:
        """
        Prepare provider-specific request format
        
        Args:
            request: Unified completion request
            
        Returns:
            Provider-specific request format
        """
        pass
    
    @abstractmethod
    def _parse_response(self, response: Any, model: str) -> CompletionResponse:
        """
        Parse provider-specific response to unified format
        
        Args:
            response: Provider-specific response
            model: Model name used
            
        Returns:
            Unified completion response
        """
        pass
    
    def __str__(self) -> str:
        return f"{self.provider_name}Client(models={len(self.supported_models)})"
    
    def __repr__(self) -> str:
        return self.__str__()
