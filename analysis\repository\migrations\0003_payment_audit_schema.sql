-- 支付审计系统数据库架构
-- 创建时间: 2025-08-20

-- Stripe事件存储表
CREATE TABLE IF NOT EXISTS stripe_events (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    stripe_event_id VARCHAR(255) NOT NULL COMMENT 'Stripe事件ID',
    event_type VARCHAR(100) NOT NULL COMMENT '事件类型',
    event_data JSON NOT NULL COMMENT '事件原始数据',
    processed TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否已处理',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE INDEX idx_stripe_event_id (stripe_event_id),
    INDEX idx_event_type (event_type),
    INDEX idx_created_at (created_at),
    INDEX idx_processed (processed)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Stripe事件存储表';

-- 支付事件详细记录表
CREATE TABLE IF NOT EXISTS payment_events (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    stripe_event_id BIGINT NOT NULL COMMENT 'Stripe事件表ID',
    user_id VARCHAR(255) NOT NULL COMMENT '用户ID',
    stripe_customer_id VARCHAR(255) NOT NULL COMMENT 'Stripe客户ID',
    stripe_charge_id VARCHAR(255) DEFAULT NULL COMMENT 'Stripe支付ID',
    stripe_payment_intent_id VARCHAR(255) DEFAULT NULL COMMENT 'Stripe支付意图ID',
    event_type ENUM('payment_succeeded', 'payment_failed', 'dispute_created', 'refund_created', 'payment_method_attached') NOT NULL COMMENT '事件类型',
    amount DECIMAL(15,2) DEFAULT NULL COMMENT '金额',
    currency VARCHAR(10) DEFAULT NULL COMMENT '货币',
    status VARCHAR(50) DEFAULT NULL COMMENT '状态',
    failure_code VARCHAR(100) DEFAULT NULL COMMENT '失败代码',
    failure_message TEXT DEFAULT NULL COMMENT '失败消息',
    outcome_type VARCHAR(50) DEFAULT NULL COMMENT '交易结果类型',
    outcome_network_status VARCHAR(50) DEFAULT NULL COMMENT '网络状态',
    outcome_reason VARCHAR(100) DEFAULT NULL COMMENT '结果原因',
    outcome_seller_message TEXT DEFAULT NULL COMMENT '商家消息',
    card_country VARCHAR(10) DEFAULT NULL COMMENT '银行卡国家',
    card_brand VARCHAR(50) DEFAULT NULL COMMENT '银行卡品牌',
    card_funding VARCHAR(50) DEFAULT NULL COMMENT '银行卡类型',
    card_last4 VARCHAR(10) DEFAULT NULL COMMENT '银行卡后四位',
    dispute_reason VARCHAR(100) DEFAULT NULL COMMENT '争议原因',
    dispute_status VARCHAR(50) DEFAULT NULL COMMENT '争议状态',
    refund_reason VARCHAR(100) DEFAULT NULL COMMENT '退款原因',
    client_ip VARCHAR(45) DEFAULT NULL COMMENT '客户端IP',
    user_agent TEXT DEFAULT NULL COMMENT '用户代理',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_stripe_event_id (stripe_event_id),
    INDEX idx_user_id (user_id),
    INDEX idx_stripe_customer_id (stripe_customer_id),
    INDEX idx_event_type (event_type),
    INDEX idx_created_at (created_at),
    INDEX idx_card_country (card_country),
    INDEX idx_failure_code (failure_code),
    INDEX idx_outcome_type (outcome_type),
    INDEX idx_user_created_at (user_id, created_at),
    FOREIGN KEY (stripe_event_id) REFERENCES stripe_events(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='支付事件详细记录表';

-- 用户支付统计表（按日汇总）
CREATE TABLE IF NOT EXISTS user_payment_daily_stats (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_id VARCHAR(255) NOT NULL COMMENT '用户ID',
    stat_date DATE NOT NULL COMMENT '统计日期',
    total_payments INT NOT NULL DEFAULT 0 COMMENT '总支付次数',
    successful_payments INT NOT NULL DEFAULT 0 COMMENT '成功支付次数',
    failed_payments INT NOT NULL DEFAULT 0 COMMENT '失败支付次数',
    disputed_payments INT NOT NULL DEFAULT 0 COMMENT '争议支付次数',
    refunded_payments INT NOT NULL DEFAULT 0 COMMENT '退款支付次数',
    total_amount DECIMAL(15,2) NOT NULL DEFAULT 0.00 COMMENT '总金额',
    successful_amount DECIMAL(15,2) NOT NULL DEFAULT 0.00 COMMENT '成功金额',
    unique_cards_used INT NOT NULL DEFAULT 0 COMMENT '使用的不同银行卡数量',
    unique_countries INT NOT NULL DEFAULT 0 COMMENT '涉及的不同国家数量',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE INDEX idx_user_date (user_id, stat_date),
    INDEX idx_stat_date (stat_date),
    INDEX idx_user_id (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户支付统计表（按日汇总）';

-- 支付失败原因统计表
CREATE TABLE IF NOT EXISTS payment_failure_stats (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_id VARCHAR(255) NOT NULL COMMENT '用户ID',
    failure_type ENUM('failure_code', 'outcome_type', 'dispute_reason') NOT NULL COMMENT '失败类型',
    failure_value VARCHAR(100) NOT NULL COMMENT '失败值',
    count INT NOT NULL DEFAULT 1 COMMENT '出现次数',
    last_occurred_at DATETIME NOT NULL COMMENT '最后发生时间',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE INDEX idx_user_failure (user_id, failure_type, failure_value),
    INDEX idx_failure_type (failure_type),
    INDEX idx_failure_value (failure_value),
    INDEX idx_last_occurred_at (last_occurred_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='支付失败原因统计表';

-- 用户银行卡使用记录表
CREATE TABLE IF NOT EXISTS user_card_usage (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_id VARCHAR(255) NOT NULL COMMENT '用户ID',
    card_fingerprint VARCHAR(255) NOT NULL COMMENT '银行卡指纹',
    card_brand VARCHAR(50) DEFAULT NULL COMMENT '银行卡品牌',
    card_country VARCHAR(10) DEFAULT NULL COMMENT '银行卡国家',
    card_funding VARCHAR(50) DEFAULT NULL COMMENT '银行卡类型',
    card_last4 VARCHAR(10) DEFAULT NULL COMMENT '银行卡后四位',
    first_used_at DATETIME NOT NULL COMMENT '首次使用时间',
    last_used_at DATETIME NOT NULL COMMENT '最后使用时间',
    usage_count INT NOT NULL DEFAULT 1 COMMENT '使用次数',
    successful_count INT NOT NULL DEFAULT 0 COMMENT '成功次数',
    failed_count INT NOT NULL DEFAULT 0 COMMENT '失败次数',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE INDEX idx_user_card (user_id, card_fingerprint),
    INDEX idx_card_country (card_country),
    INDEX idx_card_brand (card_brand),
    INDEX idx_first_used_at (first_used_at),
    INDEX idx_last_used_at (last_used_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户银行卡使用记录表';

-- 地理风险配置表
CREATE TABLE IF NOT EXISTS geo_risk_config (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    country_code VARCHAR(10) NOT NULL COMMENT '国家代码',
    risk_level ENUM('low', 'medium', 'high', 'blocked') NOT NULL DEFAULT 'low' COMMENT '风险等级',
    is_allowed TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否允许',
    description TEXT DEFAULT NULL COMMENT '描述',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE INDEX idx_country_code (country_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='地理风险配置表';

-- 插入默认的地理风险配置
INSERT INTO geo_risk_config (country_code, risk_level, is_allowed, description) VALUES
('US', 'low', 1, 'United States - Low risk'),
('CA', 'low', 1, 'Canada - Low risk'),
('GB', 'low', 1, 'United Kingdom - Low risk'),
('AU', 'low', 1, 'Australia - Low risk'),
('DE', 'low', 1, 'Germany - Low risk'),
('FR', 'low', 1, 'France - Low risk'),
('JP', 'low', 1, 'Japan - Low risk'),
('CN', 'medium', 1, 'China - Medium risk'),
('RU', 'high', 0, 'Russia - High risk, blocked'),
('IR', 'blocked', 0, 'Iran - Blocked'),
('KP', 'blocked', 0, 'North Korea - Blocked')
ON DUPLICATE KEY UPDATE 
    risk_level = VALUES(risk_level),
    is_allowed = VALUES(is_allowed),
    description = VALUES(description);
